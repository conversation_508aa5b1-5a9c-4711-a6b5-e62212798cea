cmake_minimum_required(VERSION 3.15)
project(standalone_camera VERSION 0.0.1 LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_BUILD_TYPE "Release")

# if(NOT CMAKE_C_STANDARD)
#     set(CMAKE_C_STANDARD 11)
# endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

#aux_source_directory(./src/ SRC_LIST)
 
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_ros REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
# find_package(camera_info_manager REQUIRED)
find_package(std_msgs REQUIRED)
# find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(image_transport REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(fue_interfaces REQUIRED)
find_package(std_srvs REQUIRED)

# find dependencies
find_package(OpenCV REQUIRED) 
find_package(Threads REQUIRED)


set(MVCAM_SDK_PATH "${PROJECT_SOURCE_DIR}/core/hik/lib/64")  # SDK库文件路径
set(MVCAM_INCLUDE_PATH "${PROJECT_SOURCE_DIR}/core/hik/include")  # SDK头文件路径
message("-- ------------>MVCAM_SDK_PATH: " ${MVCAM_SDK_PATH})
message("-- ------------>MVCAM_INCLUDE_PATH: " ${MVCAM_INCLUDE_PATH})

# add depend
link_directories(${MVCAM_SDK_PATH})
#link_directories(/opt/MVS/lib/64)
add_library(${PROJECT_NAME}_core #STATIC
    # impl/CvCamera
    core/v4l2/V4l2Access.cpp
    core/v4l2/V4l2Capture.cpp
    core/v4l2/V4l2Device.cpp    
    core/v4l2/V4l2MmapDevice.cpp
    core/v4l2/V4l2Output.cpp
    core/v4l2/V4l2ReadWriteDevice.cpp
    core/v4l2/V4l2CameraDev.cpp
    core/impl/CvCameraImpl.cpp 
    
    # impl/HikCamera
    core/hik/HikDev.cpp 
    core/impl/HikCameraImpl.cpp

    # bino
    core/impl/BinocularCamera.cpp

    # utils
    core/utils/definitions.cpp    
    core/utils/CameraObseverBase.cpp
)

target_include_directories(${PROJECT_NAME}_core
    PUBLIC
    core/impl
    core/v4l2
    core/utils

    core/hik    
    ${MVCAM_INCLUDE_PATH}
)

target_link_libraries(${PROJECT_NAME}_core    
    MvCameraControl    
    #pthread
    ${OpenCV_LIBS}    
    usb-1.0
    udev
)

ament_target_dependencies(${PROJECT_NAME}_core 
    "rclcpp"    
    "fue_interfaces"
) 

install(TARGETS ${PROJECT_NAME}_core
    RUNTIME DESTINATION bin
) 


#################################################################

# # add depend
# link_directories(${MVCAM_SDK_PATH})

# add_executable(trial_target
#     main/main_sample.cpp
# )

# target_include_directories(trial_target
#     PUBLIC    
#     core/hik    
#     ${MVCAM_INCLUDE_PATH}
# )

# target_link_libraries(trial_target
#     MvCameraControl    
#     #pthread
#     ${OpenCV_LIBS}    
#     usb-1.0
#     udev
# )
#################################################################

# add node camera
add_library(${PROJECT_NAME}_node SHARED
    nvida/src/cuviddec.h
    nvida/src/nvEncodeAPI.h
    nvida/src/nvcuvid.h
    nvida/src/NvEncoder.h
    nvida/src/NvEncoder.cpp
    nvida/src/NvEncoderCuda.h
    nvida/src/NvEncoderCuda.cpp
    nvida/src/NvCodecUtils.h
    ros2/CameraNode.cpp    
)

target_include_directories(${PROJECT_NAME}_node  
    PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
    ros2
)
# 设置 NVIDIA Video Codec SDK 路径
set(NV_CODEC_SDK_DIR "/home/<USER>/video-codec-sdk-master/Video_Codec_SDK_12.0.16")

# 查找库文件
find_library(NVENC_LIB
    NAMES   libnvidia-encode.so.1 libnvidia-encode.so
    PATHS   ${NV_CODEC_SDK_DIR}/Lib/linux/stubs/x86_64
    REQUIRED
)
find_library(NVENC_LIB
    NAMES   libnvcuvid.so.1 libnvcuvid.so
    PATHS   ${NV_CODEC_SDK_DIR}/Lib/linux/stubs/x86_64
    REQUIRED
)
if (NOT NVENC_LIB)
    message(FATAL_ERROR "NVENC library not found!")
endif()
if (NOT NVENC_LIB)
    message(FATAL_ERROR "NVENC_LIB library not found!")
endif()
target_link_libraries(${PROJECT_NAME}_node     
    ${PROJECT_NAME}_core   
    ${OpenCV_LIBS}
    ${NVENC_LIB}
    ${NVENC_LIB}
    #usb-1.0
    #udev
)

ament_target_dependencies(${PROJECT_NAME}_node
    "rclcpp"
    "rclcpp_components"    
    "image_transport"
    "cv_bridge"
    "sensor_msgs"
    "fue_interfaces"
    "std_srvs"
    #"camera_info_manager"    
) 

# add executable
add_executable(${PROJECT_NAME}_exe
    #main/main_hikdev.cpp
    main/main_camera_node.cpp
    #ros2/CameraNode.cpp    
)

target_include_directories(${PROJECT_NAME}_exe  
    PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
    ros2
)

target_link_libraries(${PROJECT_NAME}_exe     
    ${PROJECT_NAME}_core   
    ${PROJECT_NAME}_node   
    ${OpenCV_LIBS} 
    ${NVENC_LIB}
    ${NVENC_LIB}
    #usb-1.0
    #udev
)

# ament_target_dependencies(${PROJECT_NAME}_exe
#     "rclcpp"
#     "rclcpp_components"    
#     "image_transport"
#     "cv_bridge"
#     "sensor_msgs"
#     "fue_interfaces"
#     "std_srvs"
#     #"camera_info_manager"    
# ) 

install(TARGETS ${PROJECT_NAME}_exe
    DESTINATION lib/${PROJECT_NAME}
)









# # add node bud -- imshow crash
# add_executable(${PROJECT_NAME}_bud_node 
#     ros2/main_bud_node.cpp    
#     ros2/CameraBudNode.cpp
# )

# target_include_directories(${PROJECT_NAME}_bud_node  
#     PUBLIC
#     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
#     $<INSTALL_INTERFACE:include>
#     ros2
# )

# target_link_libraries(${PROJECT_NAME}_bud_node         
#     ${OpenCV_LIBS}     
# )

# ament_target_dependencies(${PROJECT_NAME}_bud_node 
#     "rclcpp"
#     "rclcpp_components"    
#     "image_transport"
#     "cv_bridge"
#     "sensor_msgs"
#     #"camera_info_manager"    
#     "fue_interfaces"
#     "std_srvs"
# ) 

# # add node both
# add_executable(${PROJECT_NAME}_node_both 
#     ros2/main_both.cpp
#     ros2/CameraNode.cpp
#     ros2/CameraBudNode.cpp
# )

# target_include_directories(${PROJECT_NAME}_node_both  
#     PUBLIC
#     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
#     $<INSTALL_INTERFACE:include>
#     ros2
# )

# target_link_libraries(${PROJECT_NAME}_node_both     
#     ${PROJECT_NAME}_core   
#     ${OpenCV_LIBS} 
#     #usb-1.0
#     #udev
# )

# ament_target_dependencies(${PROJECT_NAME}_node_both 
#     "rclcpp"
#     "rclcpp_components"    
#     "image_transport"
#     "cv_bridge"
#     "sensor_msgs"
#     #"camera_info_manager"    
#     "fue_interfaces"
#     "std_srvs"
# ) 

# install(TARGETS ${PROJECT_NAME}_node
#     DESTINATION lib/${PROJECT_NAME}
# )


# option(BUILD_TESTING "Enable testing support" OFF) # 使用ON/OFF决定是否开启
#
# if(BUILD_TESTING)
#     add_subdirectory(utest)
# endif()

ament_package()
