<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>standalone_camera</name>
  <version>0.0.0</version>
  <description>camera node pub stereo img</description>
  <maintainer email="<EMAIL>">yw</maintainer>
  <license>License declaration</license>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>  
  
  <depend>image_transport</depend>
  <depend>cv_bridge</depend>
  <depend>sensor_msgs</depend>
  <depend>fue_interfaces</depend>
  

  <!--
  <depend>image_transport</depend>
  <depend>camera_info_manager</depend>
  <depend>std_msgs</depend>
  -->

  
  <build_depend>geometry_msgs</build_depend>
  <build_depend>std_msgs</build_depend>

  <build_export_depend>geometry_msgs</build_export_depend>  
  <build_export_depend>std_msgs</build_export_depend>

  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>

  <build_depend>message_generation</build_depend>
  <exec_depend>message_runtime</exec_depend>


  <test_depend>rclcpp_lifecycle</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>


  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
