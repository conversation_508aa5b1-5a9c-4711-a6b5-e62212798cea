# 独立相机节点

## intro

### logic

HikCameraDev内置ping-pong,提供最新图  
HikCamera负责比对, 约束时间差, 保证实时性  

### branch dev 目前进度

* v4l2和hik都已能正确发送stereo消息  
* monitor层面会报检测失败，无法作为msg发出


### TODO

[x] 如何出图, 发布一张/n张, 时效?  
[x] ob改继承为成员, 添加实现类  
[x] 取图控制机制重写, 同时去锁去copy  
[x] local_lagacy --> V4l2 camera adapt  
[] delete obs  or  rewrite for error-handle  
[] throw runtime_error && logic_error  && TODO && FIXME  
[] 参数调优: 接口优化和配置优化  
[] 相机接口确认与实现: fps, resolution, exposure, fmt(nomo/bgr), etc…  
[] 相机接口确认与实现: 报错与重启机制(rewrite monite thread)  
[] ROS2接口/paremeters  
[] heartbeat  
[] restart interface  
[] bino的impl idiom,  
[] qt_bud合并进本仓库  

## hierarchy

### Class Diagram

```mermaid
classDiagram

    rclcppNode <|-- CameraNode
    CameraNode *-- BinoCameraBase
    CameraNode *-- CameraObseverBase 

    BinoCameraBase <-- CameraObseverBase: +observer        

    BinoCameraBase <|.. CvCamera: impl
    BinoCameraBase <|.. HikCamera: impl
    BinoCameraBase <|.. SimCamera: impl
    CvCamera *-- "2" V4l2Capture    
    HikCamera *-- "2" HikCameraDev

    CameraGrabber~T~ <|-- HikCameraDev
    %% CameraGrabber~T~ <|-- V4l2Capture

    class rclcppNode

    class CameraObseverBase{        
        +void NotifyLeftCameraFailed()
        +void NotifyRightCameraFailed()
    }

    
    class CameraNode{
        +void NotifyLeftCameraFailed()
        +void NotifyRightCameraFailed()
        -void publish_image_task()
        -void heartbeat_task()

        +std::unique_ptr~BinoCameraBase~ camera_
        -std::unique_ptr~CameraObseverBase~ cam_ob_
    }

    class BinoCameraBase{           
        +void init_cam()
        +void StopCamera()
        +bool restart_camera()        
        +void AddObserver()
        +void RemoveObserver()
    }
    class CvCamera{
        +init_cam()
        -V4l2Capture leftcamera
        -V4l2Capture rightcamera
    }    
    class HikCamera{                
        +init_cam()
        +get_one_frame_safe()
        -HikCameraDev leftcamera
        -HikCameraDev rightcamera
    }    
    class SimCamera{
        +bool is_wild
        +run()
    }

    class CameraGrabber~T~ {
        +get_latest() ImageRecord
        +~CameraGrabber()
        -grab_loop()
        -buffers_ : ImageRecord[2]
        -latest_index_ : atomic<int>
    }

    class V4l2Capture{

    }
    class HikCameraDev{

    }


```

## 备用

```bash
ros2 service call /track_fu_result fue_interfaces/srv/TrackResult "{seq_cur_idx: 0, fu_id: 1, action_step: 0, action_status: 0, fu_root_estimated: {x: 1, y: 1, z: 1}, fu_tip_estimated: {x: 2, y: 2, z: 2}}"
ros2 service call /preview_marker fue_interfaces/srv/PreviewMarker "{current_marker: {id: 1, p0: {x: 1, y: 1, z: 1}, p1: {x: 1, y: 1, z: 1}, p2: {x: 1, y: 1, z: 1}, p3: {x: 1, y: 1, z: 1}}}"
```


## swap

sh: 0: getcwd() failed: No such file or directory  
