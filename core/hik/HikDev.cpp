#include "HikDev.hpp"

#include <iostream>
#include <stdexcept>
#include <thread>

#include "CameraPingPongGrabber.hpp"
#include "rclcpp/logging.hpp"

static const rclcpp::Logger kLogger = rclcpp::get_logger("HikCameraDev");

HikCameraDev::HikCameraDev()
    : CameraGrabber()
    , handle_(nullptr)
    , initialized_(false)
    , grabbing_(false)
    , width_(0)
    , height_(0)
    , pixel_type_(0)
    , payload_size_(0)
    , frame_info_{}
{
}

HikCameraDev::~HikCameraDev()
{
    stop_grab();
    if (handle_) {
        MV_CC_DestroyHandle(handle_);
        handle_ = nullptr;
    }
}

bool HikCameraDev::init(int index)
{
#define use_const_serial_string
    //TODO: move into config
    std::string serial_str;
    const auto left_serial = std::string("*********");
    const auto right_serial = std::string("*********");

    if (index == 0) {
        dev_name_ = "left";
        dev_idx_ = index;
        serial_str = left_serial;
    }
    else if (index == 1) {
        dev_name_ = "right";
        dev_idx_ = index;
        serial_str = right_serial;
    }
    else {
        std::cout << "MV_CC_Initialize() failed!! wrong idx=" << index << std::endl;
        return false;
    }

    //init
    auto nRet = MV_CC_Initialize();
    if (MV_OK != nRet) {
        std::cout << "MV_CC_Initialize() failed!!" << std::endl;
        return false;
    }

    //enum
    MV_CC_DEVICE_INFO_LIST dev_list = {};
    if (MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, &dev_list) != MV_OK ||
        dev_list.nDeviceNum == 0) {
        return false;
    }
    if (index >= static_cast<int>(dev_list.nDeviceNum)) {
        return false;
    }

    //create handle
    MV_CC_DEVICE_INFO *pDevInfo;
#ifndef use_const_serial_string
    pDevInfo = dev_list.pDeviceInfo[index];
#else
    for (MV_CC_DEVICE_INFO *info_ptr : dev_list.pDeviceInfo) {
        if (info_ptr->nTLayerType != MV_USB_DEVICE) {
            continue;
        }
        if (serial_str == std::string(reinterpret_cast<char *>(
                              info_ptr->SpecialInfo.stUsb3VInfo.chSerialNumber))) {
            pDevInfo = info_ptr;
            break;
        }
    }
#endif

    if (MV_CC_CreateHandle(&handle_, pDevInfo) != MV_OK) {
        return false;
    }

    if (MV_CC_OpenDevice(handle_) != MV_OK) {
        return false;
    }

    //set dev_model_name_
    if (pDevInfo->nTLayerType == MV_GIGE_DEVICE) {
        dev_model_name_ =
            std::string(reinterpret_cast<char *>(pDevInfo->SpecialInfo.stGigEInfo.chModelName));
    }
    else if (pDevInfo->nTLayerType == MV_USB_DEVICE) {
        dev_model_name_ =
            std::string(reinterpret_cast<char *>(pDevInfo->SpecialInfo.stUsb3VInfo.chModelName));
    }

    std::cout << ": dev_name_=" << dev_name_
              << ", chSerialNumber=" << pDevInfo->SpecialInfo.stUsb3VInfo.chSerialNumber
              << ", Model Name: " << dev_model_name_ << std::endl;

    initialized_ = true;
    return true;
}

bool HikCameraDev::configure(int width, int height, float fps, float exposure_ms,
                             unsigned int pixel_type)
{
    if (!initialized_) {
        return false;
    }

    auto res = MV_OK;
    //trigger
    MV_CC_SetTriggerMode(handle_, MV_TRIGGER_MODE_OFF);
    if (MV_OK != res) {
        std::cout << "MV_CC_SetTriggerMode失败! nRet [" << std::hex << res << std::dec << std::endl;
        return false;
    }

    //fmt
    res = MV_CC_SetEnumValue(handle_, "PixelFormat", pixel_type);
    if (res != MV_OK) {
        std::cout << __FUNCTION__ << ": PixelFormat" << " failed!" << std::hex << res << std::dec
                  << std::endl;
        return false;
    }

    //resolution
    if (!set_resolution(width, height)) {
        return false;
    }

    //获取和设置曝光时间
    MVCC_FLOATVALUE stExposureTime = {};
    res = MV_CC_GetFloatValue(handle_, "ExposureTime", &stExposureTime);
    if (MV_OK == res) {
        //float exposureTime = 10000.0f;  //默认10ms
        float exposureTime = exposure_ms * 1000;
        if (exposureTime < stExposureTime.fMin) exposureTime = stExposureTime.fMin;
        if (exposureTime > stExposureTime.fMax) exposureTime = stExposureTime.fMax;

        std::cout << "曝光时间范围: [" << stExposureTime.fMin << ", " << stExposureTime.fMax
                  << "], 当前设置: " << exposureTime << ", 单位: us" << std::endl;

        //nRet = MV_CC_SetFloatValue(handle_, "ExposureTime", exposureTime);
        res = MV_CC_SetExposureTime(handle_, exposureTime);
        if (MV_OK != res) {
            std::cout << "设置曝光时间失败! nRet [" << res << "]" << std::endl;
            return false;
        }
    }
    //获取和设置增益
    MVCC_FLOATVALUE stGain = {};
    res = MV_CC_GetFloatValue(handle_, "Gain", &stGain);
    if (MV_OK == res) {
        std::cout << "增益范围: [" << stGain.fMin << ", " << stGain.fMax
                  << "], 当前值: " << stGain.fCurValue << std::endl;

        if (stGain.fCurValue < stGain.fMin || stGain.fCurValue > stGain.fMax) {
            //float gain = stGain.fMin;  //使用最小值
            float gain = 5;
            res = MV_CC_SetFloatValue(handle_, "Gain", gain);
            if (MV_OK != res) {
                std::cout << "设置增益失败! nRet [" << res << "]" << std::endl;
                return false;
            }
            else {
                std::cout << "设置增益: " << gain << std::endl;
            }
        }
    }

    //fps
    res = MV_CC_SetFloatValue(handle_, "AcquisitionFrameRate", fps);
    if (res != MV_OK) {
        std::cout << __FUNCTION__ << ": AcquisitionFrameRate" << " failed!" << std::hex << res
                  << std::dec << std::endl;
        return false;
    }

    width_ = width;
    height_ = height;
    pixel_type_ = pixel_type;

    //MVCC_INTVALUE val = {};
    //if (MV_CC_GetIntValue(handle_, "PayloadSize", &val) == MV_OK) {
    //    payload_size_ = val.nCurValue;
    //    buffer_.resize(payload_size_);
    //    return true;
    //}

    return true;
}

bool HikCameraDev::set_resolution(int width, int height)
{

    MVCC_INTVALUE old_w, old_h, new_w, new_h;
    MV_CC_GetWidth(handle_, &old_w);
    MV_CC_GetHeight(handle_, &old_h);

    auto res = MV_CC_SetIntValue(handle_, "Width", width);
    if (res != MV_OK) {
        std::cout << __FUNCTION__ << ": set Width failed! res=0x" << std::hex << res << std::dec
                  << std::endl;
        throw std::runtime_error("set width failed!");
        return false;
    }

    res = MV_CC_SetIntValue(handle_, "Height", height);
    if (res != MV_OK) {
        std::cout << __FUNCTION__ << ": set Height failed! res=0x" << std::hex << res << std::dec
                  << std::endl;
        throw std::runtime_error("set height failed!");
        return false;
    }

    //resolution check
    MV_CC_GetWidth(handle_, &new_w);
    MV_CC_GetHeight(handle_, &new_h);

    if ((int)new_w.nCurValue != width || (int)new_h.nCurValue != height) {
        std::cout << __FUNCTION__ << ": FAILED!! unspported resolution!!" << std::endl;
        return false;
    }

    //resize payload and buffer
    img_.image = cv::Mat::zeros(height, width, CV_8UC3);

    MVCC_INTVALUE val = {};
    if (MV_CC_GetIntValue(handle_, "PayloadSize", &val) != MV_OK) {
        return false;
    }
    payload_size_ = val.nCurValue;
    buffer_.resize(payload_size_);

    std::cout << __FUNCTION__ << ": reset " << dev_name_ << "'s resolution from ["
              << old_w.nCurValue << "*" << old_h.nCurValue << "]->[" << width << "*" << height
              << "]." << std::endl;

    return true;
}

std::pair<int, int> HikCameraDev::get_resolution()
{
    auto r = std::pair<int, int>{-1, -1};
    if (handle_ == nullptr) {
        return r;
    }
    MVCC_INTVALUE w{}, h{};
    auto r1 = MV_CC_GetIntValue(handle_, "Width", &w);
    auto r2 = MV_CC_GetIntValue(handle_, "Height", &h);

    if (r1 == MV_OK) {
        r.first = w.nCurValue;
    }
    if (r2 == MV_OK) {
        r.second = h.nCurValue;
    }

    return r;
}

bool HikCameraDev::set_pixel_format(PixelFormat fmt)
{
    MvGvspPixelType mvfmt;
    if (fmt == PixelFormat::Mono8) {
        mvfmt = PixelType_Gvsp_Mono8;
    }
    else {
        return false;
    }

    return MV_CC_SetEnumValue(handle_, "PixelFormat", mvfmt) == MV_OK;
}

unsigned HikCameraDev::get_pixel_type()
{
    MVCC_ENUMVALUE fmt = {};
    MV_CC_GetPixelFormat(handle_, &fmt);
    return fmt.nCurValue;
}

PixelFormat HikCameraDev::get_pixel_format()
{
    auto type = get_pixel_type();

    if (type == PixelType_Gvsp_Mono8) {
        return PixelFormat::Mono8;
    }
    else if (type == PixelType_Gvsp_BGR8_Packed) {
        return PixelFormat::BGR8;
    }
    else {
        return PixelFormat::Default;
    }
}

bool HikCameraDev::set_fps_exposure_safe(int fps, int exposure)
{
    auto ret = false;
    ret = MV_CC_SetFloatValue(handle_, "AcquisitionFrameRate", fps);
    ret = ret && MV_CC_SetFloatValue(handle_, "ExposureTime", exposure * 1000.0f);
    return ret;
}

bool HikCameraDev::set_fps(int fps)
{
    return MV_CC_SetFloatValue(handle_, "AcquisitionFrameRate", fps);
}

bool HikCameraDev::set_exposure(int exposure)
{
    return MV_CC_SetFloatValue(handle_, "ExposureTime", exposure * 1000.0f);
}

int HikCameraDev::get_fps()
{
    MVCC_FLOATVALUE fps{};
    MV_CC_GetFrameRate(handle_, &fps);
    return fps.fCurValue;
}

std::string HikCameraDev::get_device_model_name()
{
    return dev_model_name_;
}

bool HikCameraDev::start_grab()
{
    if (!initialized_) {
        return false;
    }
    if (MV_CC_StartGrabbing(handle_) != MV_OK) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    grabbing_ = true;
    return true;
}

void HikCameraDev::stop_grab()
{
    if (grabbing_) {
        MV_CC_StopGrabbing(handle_);
        grabbing_ = false;
    }

    if (initialized_) {
        MV_CC_CloseDevice(handle_);
        initialized_ = false;
    }
}

ImageRecord HikCameraDev::get_one_frame_safe()
{
    if (!is_opened()) {
        return {};
    }
    //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": before capture");
    auto ret = MV_CC_GetOneFrameTimeout(handle_, buffer_.data(), payload_size_, &frame_info_, 5000);
    img_.timepoint = std::chrono::steady_clock::now();
    //std::cout << __FUNCTION__ << ": " << dev_name_ << ": 获取一帧图像成功: Width["
    //          << frame_info_.nWidth << "] Height[" << frame_info_.nHeight << "] FrameNum["
    //          << frame_info_.nFrameNum << "], nFrameLenEx=" << frame_info_.nFrameLenEx <<
    //          std::endl;
    //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": after capture");
    bool frame_cnt_flag;
    if (frame_cnt_ != std::numeric_limits<unsigned>::max() &&
        frame_cnt_ + 1 == frame_info_.nFrameCounter) {
        frame_cnt_ = frame_info_.nFrameCounter;
        frame_cnt_flag = true;
    }
    else {
        frame_cnt_flag = false;
    }

    if (ret != MV_OK) {
        std::cout << __FUNCTION__ << ": ret=0x" << std::hex << ret << std::dec << std::endl;

        if (!frame_cnt_flag) {
            std::cout << __FUNCTION__ << ": wrong frame_cnt_!!" << std::endl;
            return {};
        }

        if (frame_info_.nWidth == 0 || frame_info_.nHeight == 0) {
            std::cout << __FUNCTION__ << ": wrong resolution!!" << std::endl;
            return {};
        }

        if ((unsigned)ret == MV_E_NODATA) {
            return {};
        }
    }

    //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": before to cv::Mat");
    auto cv_fmt = CV_8UC1;
    static cv::Mat tmp_bayer_mat;
    if (pixel_type_ == PixelType_Gvsp_BGR8_Packed || pixel_type_ == PixelType_Gvsp_RGB8_Packed) {
        cv_fmt = CV_8UC3;
    }
    else if (pixel_type_ == PixelType_Gvsp_BayerRG8) {
        cv_fmt = CV_8UC1;
        tmp_bayer_mat =
            cv::Mat(frame_info_.nHeight, frame_info_.nWidth, cv_fmt, buffer_.data()).clone();
    }

    if (pixel_type_ == PixelType_Gvsp_BayerRG8) {
        cv::cvtColor(tmp_bayer_mat, img_.image, cv::COLOR_BayerBG2BGR);
    }
    else {
        img_.image =
            cv::Mat(frame_info_.nHeight, frame_info_.nWidth, cv_fmt, buffer_.data()).clone();
    }

    //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": after to cv::Mat");

    //mark it
    // cv::putText(img_.image,
    //             dev_name_,
    //             cv::Point(0, 50),
    //             cv::FONT_HERSHEY_DUPLEX,
    //             1,
    //             cv::Scalar(255, 255, 0));
    //save
    //auto name = std::string("xxx_cam_") + std::string(dev_name_) + "_xx.jpg";
    //cv::imwrite(name, img_.image);

    return img_;
}

bool HikCameraDev::is_opened() const
{
    return initialized_ && grabbing_;
}

bool HikCameraDev::set_brightness(int brightness)
{
    MVCC_INTVALUE v{};
    MV_CC_GetBrightness(handle_, &v);

    std::cout << __FUNCTION__ << ": [" << v.nMin << ", " << v.nMax << "], 当前设置: " << v.nCurValue
              << "." << std::endl;

    auto rt = MV_CC_SetBrightness(handle_, brightness);
    std::cout << __FUNCTION__ << "set new value=" << brightness << (rt ? " succ." : " failed!!")
              << std::endl;
    return rt;
}

/*





    end





*/

#ifdef USE_OLD_CODE_XXXXXXXXXXXXXXXXXXXXXX

//设置相机参数
bool HikCameraDev::SetParameters()
{
    int nRet;
    //设置触发模式为off
    nRet = MV_CC_SetEnumValue(handle_, "TriggerMode", 0);
    if (MV_OK != nRet) {
        std::cout << "设置触发模式失败! nRet [" << nRet << "]" << std::endl;
        return false;
    }
    MV_CC_SetTriggerMode(handle_, MV_TRIGGER_MODE_OFF);
    if (MV_OK != nRet) {
        std::cout << "MV_CC_SetTriggerMode失败! nRet [" << nRet << "]" << std::endl;
        return false;
    }

    //设置像素格式为BGR8
    // MVCC_ENUMVALUE fmt_enum = {};
    // auto sss = MV_CC_GetPixelFormat(handle_, &fmt_enum);

    //auto pixel_fmt = PixelType_Gvsp_BGR8_Packed;
    //auto pixel_fmt = PixelType_Gvsp_RGB8_Packed;
    auto pixel_fmt = PixelType_Gvsp_Mono8;
    nRet = MV_CC_SetEnumValue(handle_, "PixelFormat", pixel_fmt);

    nRet = MV_CC_SetPixelFormat(handle_, pixel_fmt);
    if (MV_OK != nRet) {
        std::cout << "设置像素格式失败! nRet [" << nRet << "]" << std::endl;
        return false;
    }
    std::cout << "设置像素格式为: " << std::dec << pixel_fmt << std::endl;

    //设置宽度 高度
    MV_CC_SetIntValue(handle_, "Width", 640);
    MV_CC_SetIntValue(handle_, "Height", 480);

    //获取和设置曝光时间
    MVCC_FLOATVALUE stExposureTime = {};
    nRet = MV_CC_GetFloatValue(handle_, "ExposureTime", &stExposureTime);
    if (MV_OK == nRet) {
        float exposureTime = 10000.0f;  //默认10ms
        if (exposureTime < stExposureTime.fMin) exposureTime = stExposureTime.fMin;
        if (exposureTime > stExposureTime.fMax) exposureTime = stExposureTime.fMax;

        std::cout << "曝光时间范围: [" << stExposureTime.fMin << ", " << stExposureTime.fMax
                  << "], 当前设置: " << exposureTime << std::endl;

        //nRet = MV_CC_SetFloatValue(handle_, "ExposureTime", exposureTime);
        nRet = MV_CC_SetExposureTime(handle_, exposureTime);
        if (MV_OK != nRet) {
            std::cout << "设置曝光时间失败! nRet [" << nRet << "]" << std::endl;
            return false;
        }
    }
    //获取和设置增益
    MVCC_FLOATVALUE stGain = {};
    nRet = MV_CC_GetFloatValue(handle_, "Gain", &stGain);
    if (MV_OK == nRet) {
        std::cout << "增益.范围: [" << stGain.fMin << ", " << stGain.fMax
                  << "], 当前值: " << stGain.fCurValue << std::endl;

        if (stGain.fCurValue < stGain.fMin || stGain.fCurValue > stGain.fMax) {
            float gain = stGain.fMin;  //使用最小值
            nRet = MV_CC_SetFloatValue(handle_, "Gain", gain);
            if (MV_OK != nRet) {
                std::cout << "设置增益失败! nRet [" << nRet << "]" << std::endl;
                return false;
            }
        }
    }
    //获取和设置帧率
    MVCC_FLOATVALUE stFrameRate = {};
    MV_CC_SetBoolValue(handle_, "AcquisitionFrameRateEnable", true);
    nRet = MV_CC_GetFloatValue(handle_, "AcquisitionFrameRate", &stFrameRate);
    if (MV_OK == nRet) {
        float frameRate = 20.0f;  //默认30fps
        if (frameRate < stFrameRate.fMin) frameRate = stFrameRate.fMin;
        if (frameRate > stFrameRate.fMax) frameRate = stFrameRate.fMax;

        std::cout << "帧率范围: [" << stFrameRate.fMin << ", " << stFrameRate.fMax
                  << "], 当前设置: " << frameRate << std::endl;

        nRet = MV_CC_SetFloatValue(handle_, "AcquisitionFrameRate", frameRate);
        if (MV_OK != nRet) {
            std::cout << "设置帧率失败! nRet [" << nRet << "]" << std::endl;
            //这个错误不影响主要功能，可以继续
        }
    }
    return true;
}

//开始取图
bool HikCameraDev::StartGrabbing()
{
    //开始取流
    int nRet = MV_CC_StartGrabbing(handle_);
    if (MV_OK != nRet) {
        std::cout << "开始取流失败! nRet [" << nRet << "]" << std::endl;
        return false;
    }
    //获取数据包大小
    MVCC_INTVALUE stParam;
    nRet = MV_CC_GetIntValue(handle_, "PayloadSize", &stParam);
    if (MV_OK != nRet) {
        std::cout << "获取数据包大小失败! nRet [" << nRet << "]" << std::endl;
        return false;
    }
    //分配资源
    nDataSize = stParam.nCurValue + 2048;
    //nDataSize = 640 * 480 * 4 + 2048;
    pData = (unsigned char *)malloc(nDataSize);
    if (pData == nullptr) {
        std::cout << "内存分配失败!" << std::endl;
        return false;
    }
    std::cout << __FUNCTION__ << ": done" << std::endl;
    return true;
}

//获取一帧图像
size_t HikCameraDev::read(unsigned char *buffer, size_t buffer_size)
{
    if (handle_ == nullptr || buffer == nullptr) {
        return -1;
    }
    int nRet = MV_CC_GetOneFrameTimeout(handle_, buffer, buffer_size, &stImageInfo, 1000);
    if (MV_OK != nRet) {
        std::cout << dev_name_ << ": 获取一帧图像失败! nRet [" << std::hex << nRet << "]"
                  << std::dec << std::endl;
        return -1;
    }
    std::cout << dev_name_ << ": 获取一帧图像成功: Width[" << stImageInfo.nWidth << "] Height["
              << stImageInfo.nHeight << "] FrameNum[" << stImageInfo.nFrameNum
              << "], nFrameLenEx=" << stImageInfo.nFrameLenEx << std::endl;

    return stImageInfo.nFrameLenEx;
}

#endif
