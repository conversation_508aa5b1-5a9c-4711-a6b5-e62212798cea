﻿;@~chinese
;该配置文件列出了部分可配置的参数，其他可配置参数请参考软件安装路径下 /opt/(软件名)/doc/工业相机SDK可配置化参数表.xlsx
;修改配置后上层应用程序要重新启动
;不分设备类型的通用参数
;@~english
;The configuration file lists some configurable parameters, other configurable parameters please refer to the software installation path /opt/(Software)/doc/Camera SDK configurable parameter table.xlsx
;When modifying the configuration, the upper application need restarted
;Generic parameters that do not distinguish device types
[COMMON]
;@~chinese
;设置SDK内部图像缓存节点个数，若调用接口(MV_CC_SetImageNodeNum)主动设置,则此参数无效;若是外部分配缓存模式（即调用MV_CC_RegisterBuffer）此参数也无效;不支持MV_CAMERALINK_DEVICE 类型的设备
;SDK实际分配的节点个数 = SDK内部预分配的个数 + ImageNodeNum;
;不同相机因为取流方式不同，SDK内部预分配的个数不同：比如 双U内部分配默认3个节点
;@~english
;Set the number of image cache nodes within the SDK, and if you call the interface (MV_CC_SetImageNodeNum or MV_CC_RegisterBuffer), the parameter is invalid; not support MV_CAMERALINK_DEVICE device
;The actual number of image cache nodes allocated by the SDK = the number of pre allocated nodes within the SDK + ImageNodeNum;
;Different cameras default to different pre allocated nodes. For example, for dual USB camera, the default number is 3
ImageNodeNum=1  

;@~chinese
;网口相机相关参数
;@~english
;The parameters of Gige camera
[GIGE]
;@~chinese
;设置GVCP命令超时时间，默认500ms，范围：0-10000ms
;@~english
;Set GVCP command timeout time, the default value is 500ms, range: 0-10000ms
GvcpTimeout=500

;@~chinese
;U口相机相关参数
;@~english
;The parameters of U3V camera
[U3V]
;@~chinese
;设置U3V的传输包大小，Byte，默认为1M，rang：>=0x400
;@~english
;Set transfer size of U3V device, the unit is Byte, Default 1M，rang: >=0x400
TransferSize=1048576
;@~chinese
;设置流包间隔超时时间，默认50ms，当超时时间＞1000ms会关闭断流恢复机制
;@~english
;Set stream payload interval timeout, Default 50ms;
StreamPayloadTimeout=50
;@~chinese
;设置出流寄存器读写超时时间，默认30ms
;@~english
;Set stream control register timeout, Default 30ms
SIControlRegTimeout=30
;@~chinese
;设置控制寄存器读写超时时间，默认1000ms
;除SI寄存器外
;@~english
;Set control Reg  timeout ms, Default 1000ms
;Except SI Reg
SyncTimeout=1000

;@~chinese
;CameraLink相机相关参数
;@~english
;The parameters of CameraLink camera
[CAML]

;@~chinese
;图像处理相关的参数
;@~english
;The parameters of image processing
[IMG_PROCESSING]
;@~chinese
;设置插值算法类型，0-快速 1-均衡 2-最优 3-最优+（默认为均衡）
;@~english
;Interpolation algorithm type setting, 0-Fast 1-Equilibrium 2-Optimal 3-Optimal+(the default value is 1-Equilibrium)
BayerCvtQuality=1
;@~chinese
;设置插值算法处理线程个数，0-自适应 其他-具体线程个数(1,2,3,...)（默认线程个数为4）
;@~english
;Set the interpolation algorithm of thread handle count, 0-self-adapting, other-number of specific thread count(1,2,3,...) (the default thread count is 4)
BayerCvtThreadNum=4