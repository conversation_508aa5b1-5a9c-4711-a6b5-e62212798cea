#ifndef CAMERA_NODE_CORE_HIK_HIKDEV_HPP_
#define CAMERA_NODE_CORE_HIK_HIKDEV_HPP_
#include <cstring>
#include <iostream>
#include <map>
#include <string>

#include <opencv2/opencv.hpp>

#include "CameraParams.h"  // for MVCC_INTVALUE
#include "MvCameraControl.h"
#include "PixelType.h"  // for PixelType_Gvsp_BGR8_Packed

#include "CameraPingPongGrabber.hpp"
#include "definitions.hpp"

class HikCameraDev : public CameraGrabber<HikCameraDev> {
public:
    HikCameraDev();
    ~HikCameraDev();

    bool init(int index = 0);
    bool configure(int width, int height, float fps, float exposure_ms,
                   unsigned int pixel_type = PixelType_Gvsp_Mono8);

    bool set_resolution(int width, int height);
    std::pair<int, int> get_resolution();

    bool set_pixel_format(PixelFormat fmt);
    unsigned get_pixel_type();
    PixelFormat get_pixel_format();

    bool set_fps_exposure_safe(int fps, int exposure);

    bool set_fps(int fps);
    int get_fps();

    bool set_exposure(int exposure);
    bool set_brightness(int brightness);

    std::string get_device_model_name();

    bool start_grab();
    void stop_grab();
    bool is_opened() const;

    ImageRecord get_one_frame_safe();  //自动转换为 cv Mat

private:
    void *handle_;
    bool initialized_;
    bool grabbing_;
    unsigned int width_;
    unsigned int height_;
    unsigned int pixel_type_;  //defined by Hik, same as PixelFormat
    unsigned int payload_size_;
    std::vector<unsigned char> buffer_;
    MV_FRAME_OUT_INFO_EX frame_info_;

    std::string dev_name_;
    std::string dev_model_name_;
    unsigned frame_cnt_;
    int dev_idx_;

    ImageRecord img_;
};

#endif  //#define CAMERA_NODE_CORE_HIK_HIKDEV_HPP_
