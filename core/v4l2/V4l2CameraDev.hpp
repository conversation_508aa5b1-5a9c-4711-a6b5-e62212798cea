#ifndef CAMERA_NODE_CORE_V4L2_V4L2_DEV_HPP_
#define CAMERA_NODE_CORE_V4L2_V4L2_DEV_HPP_
#include <cstring>
#include <iostream>
#include <map>
#include <string>

#include <opencv2/opencv.hpp>

#include "CameraPingPongGrabber.hpp"
#include "V4l2Capture.h"
#include "definitions.hpp"

class V4l2CameraDev : public CameraGrabber<V4l2CameraDev> {
public:
    V4l2CameraDev();
    ~V4l2CameraDev();

    bool start(int index, V4L2DeviceParameters &param);
    void stop();

    bool set_resolution(int width, int height);
    std::pair<int, int> get_resolution();

    bool set_pixel_format(PixelFormat fmt);
    PixelFormat get_pixel_format();

    std::string get_device_model_name();
    int get_fps();

    void set_brightness(int brightness);

    ImageRecord get_one_frame_safe();  //自动转换为 cv Mat

    bool is_opened() const;

private:
    std::unique_ptr<V4l2Capture> handle_;

    bool initialized_;
    bool grabbing_;
    //unsigned int width_;
    //unsigned int height_;
    PixelFormat pixel_fmt_;
    int fps_;

    std::string dev_name_;
    std::string dev_model_name_;
    int dev_idx_;

    std::vector<char> vec_buff_left_;
    ImageRecord img_;

private:
    static int query_zoom_absolute(int id);
};

#endif  //CAMERA_NODE_CORE_V4L2_V4L2_DEV_HPP_
