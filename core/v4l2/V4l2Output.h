#ifndef V4l2Output_H
#define V4l2Output_H


#include "V4l2Access.h"

// ---------------------------------
// V4L2 Output
// ---------------------------------
class V4l2Output : public V4l2Access
{		
	protected:
		explicit V4l2Output(V4l2Device* device);

	public:
		static V4l2Output* create(const V4L2DeviceParameters & param);
		virtual ~V4l2Output();
	
		size_t write(char* buffer, size_t bufferSize);
		bool   isWritable(timeval* tv);
		bool   startPartialWrite();
		size_t writePartial(char* buffer, size_t bufferSize);
		bool   endPartialWrite();
};

#endif //V4l2Output_H

