#include "V4l2CameraDev.hpp"

#include <charconv>
#include <chrono>
#include <iostream>
#include <sstream>
#include <stdexcept>
#include <string>
#include <thread>

#include <dirent.h>
#include <fcntl.h>  //open
#include <stdio.h>
#include <unistd.h>  //close

#include <libudev.h>
#include <libusb-1.0/libusb.h>
#include <linux/videodev2.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "V4l2Capture.h"
#include "rclcpp/logging.hpp"

using namespace std::chrono_literals;

static const rclcpp::Logger kLogger = rclcpp::get_logger("V4l2CameraDev");

V4l2CameraDev::V4l2CameraDev()
    : handle_(nullptr)
    , initialized_(false)
    , grabbing_(false)
    , pixel_fmt_(PixelFormat::Default)
    , dev_model_name_("general-usb-cam")
    , dev_idx_(-1)
    , img_{}
{
    vec_buff_left_.resize(4000 * 3000);
    std::fill(vec_buff_left_.begin(), vec_buff_left_.end(), 0);
}

V4l2CameraDev::~V4l2CameraDev()
{
    stop();
}

bool V4l2CameraDev::start(int index, V4L2DeviceParameters &param)
{
    dev_idx_ = -1;
    if (index < 0) {
        return false;
    }

    handle_ = std::unique_ptr<V4l2Capture>(V4l2Capture::create(param));
    if (handle_ == nullptr) {
        return false;
    }

    fps_ = param.m_fps;
    initialized_ = true;
    grabbing_ = true;
    return true;
}

void V4l2CameraDev::stop()
{
    grabbing_ = false;
    std::this_thread::sleep_for(50ms);
    initialized_ = false;
    handle_.reset();
}

bool V4l2CameraDev::set_resolution(int width, int height)
{
    if (handle_ == nullptr) {
        return false;
    }

    auto fmt = handle_->getFormat();
    return handle_->setFormat(fmt, width, height) == 0;
}

std::pair<int, int> V4l2CameraDev::get_resolution()
{
    return {handle_->getWidth(), handle_->getHeight()};
}

bool V4l2CameraDev::set_pixel_format([[maybe_unused]] PixelFormat fmt)
{
    //only V4L2_PIX_FMT_MJPEG is supported

    //unsigned nfmt;
    //if (fmt == PixelFormat::BGR8) {
    //    nfmt = V4L2_PIX_FMT_BGR24;
    //}
    //else if (fmt == PixelFormat::Mono8) {
    //    nfmt = V4L2_PIX_FMT_GREY;
    //}
    //else {
    //    return false;
    //}

    //auto w = handle_->getWidth();
    //auto h = handle_->getHeight();
    //if (handle_->setFormat(nfmt, w, h) == 0) {
    //    pixel_fmt_ = fmt;
    //    return true;
    //}
    return false;
}

PixelFormat V4l2CameraDev::get_pixel_format()
{
    return pixel_fmt_;
}

std::string V4l2CameraDev::get_device_model_name()
{
    return dev_model_name_;
}

int V4l2CameraDev::get_fps()
{
    return fps_;
}

void V4l2CameraDev::set_brightness(int brightness)
{
    handle_->SetBrightness(brightness);
    return;
}

ImageRecord V4l2CameraDev::get_one_frame_safe()
{
    if (!is_opened()) {
        return {};
    }

    timeval tv;
    tv.tv_sec = 1;
    tv.tv_usec = 0;
    if (!handle_->isReadable(&tv)) {
        RCLCPP_INFO(kLogger, "cvcamera restart left 5");
        return {};
    }

    std::fill(vec_buff_left_.begin(), vec_buff_left_.end(), 0);

    //RCLCPP_INFO_STREAM(kLogger,
    //                   ": " << dev_name_ << dev_idx_ << ", initialized_=" << initialized_
    //                        << ", grabbing_=" << grabbing_);

    int rsize = handle_->read(vec_buff_left_.data(), 4000 * 3000 - 1);
    if (rsize == -1) {

        RCLCPP_INFO(kLogger, "cvcamera restart left 2");

        return {};
    }
    else if (rsize == 0) {

        RCLCPP_INFO(kLogger, "cvcamera restart left 3");

        return {};
    }

    if (handle_->getFormat() != V4L2_PIX_FMT_MJPEG) {

        RCLCPP_INFO(kLogger, "cvcamera restart left 4");
        return {};
    }

    img_.timepoint = std::chrono::steady_clock::now();
    img_.image = cv::imdecode(vec_buff_left_, cv::IMREAD_COLOR);
    //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": after to cv::Mat");
    return img_;
}

bool V4l2CameraDev::is_opened() const
{
    return initialized_ && grabbing_;
}

int V4l2CameraDev::query_zoom_absolute(int id)
{
    std::string device = "/dev/video" + std::to_string(id);
    int fd = open(device.c_str(), O_RDWR);
    if (fd == -1) {
        return -1;
    }

    struct v4l2_control control;
    memset(&control, 0, sizeof(control));
    control.id = V4L2_CID_ZOOM_ABSOLUTE;  //设置控制ID为zoom_absolute

    if (ioctl(fd, VIDIOC_G_CTRL, &control) == -1) {
        close(fd);
        return -1;
    }

    close(fd);
    return control.value;
}
