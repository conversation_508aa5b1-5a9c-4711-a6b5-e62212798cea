#ifndef CAMERANODE_CORE_UTILS_CAMERA_PINGPONG_GRABBER_HPP_
#define CAMERANODE_CORE_UTILS_CAMERA_PINGPONG_GRABBER_HPP_

#include <array>
#include <atomic>
#include <thread>

#include <opencv2/opencv.hpp>

#include "definitions.hpp"

template<typename Derived>
class CameraGrabber {
public:
    CameraGrabber();
    ~CameraGrabber();

    ImageRecord get_latest() const;

private:
    void grab_loop();
    Derived &derived();
    const Derived &derived() const;

private:
    std::atomic<bool> running_{false};
    std::atomic<int> latest_index_{0};
    std::array<ImageRecord, 2> buffers_;
    std::thread grab_thread_;
};

//==================== IMPLEMENTATION ====================

template<typename Derived>
Camera<PERSON>rabber<Derived>::Camera<PERSON>rabber()
{
    running_.store(true);
    grab_thread_ = std::thread(&CameraGrabber::grab_loop, this);
}

template<typename Derived>
CameraGrabber<Derived>::~CameraGrabber()
{
    running_.store(false);
    if (grab_thread_.joinable()) {
        grab_thread_.join();
    }
}

template<typename Derived>
ImageRecord CameraGrabber<Derived>::get_latest() const
{
    int index = latest_index_.load(std::memory_order_acquire);
    return buffers_[index];
}

template<typename Derived>
void CameraGrabber<Derived>::grab_loop()
{
    int write_index = 0;
    while (running_.load(std::memory_order_relaxed)) {
        ImageRecord record = derived().get_one_frame_safe();
        buffers_[write_index] = std::move(record);
        latest_index_.store(write_index, std::memory_order_release);
        write_index = 1 - write_index;
    }
}

template<typename Derived>
Derived &CameraGrabber<Derived>::derived()
{
    return static_cast<Derived &>(*this);
}

template<typename Derived>
const Derived &CameraGrabber<Derived>::derived() const
{
    return static_cast<const Derived &>(*this);
}

#endif  //CAMERANODE_CORE_UTILS_CAMERA_PINGPONG_GRABBER_HPP_
