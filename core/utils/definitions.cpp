#include "definitions.hpp"

ImageRecord::ImageRecord()
    : image()
    , timepoint(TimePoint::min())
{
}

void ImageRecord::clear()
{
    image.release();
    timepoint = TimePoint::min();
}

BinocularRecord::BinocularRecord()
    : left_image()
    , right_image()
    //, bino_image()
    , timepoint(TimePoint::min())
{
}

void BinocularRecord::clear()
{
    left_image.release();
    right_image.release();
    timepoint = TimePoint::min();
}