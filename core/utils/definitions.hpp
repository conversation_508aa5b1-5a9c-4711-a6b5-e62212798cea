#ifndef CAMERA_NODE_CORE_UTILS_DEFINITIONS_HPP_
#define CAMERA_NODE_CORE_UTILS_DEFINITIONS_HPP_

#include <algorithm>
#include <iostream>
#include <numeric>
#include <string>

#include <eigen3/Eigen/Dense>
#include <opencv2/core/types.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <yaml-cpp/yaml.h>

using TimePoint = std::chrono::time_point<std::chrono::steady_clock>;

enum class CameraTypeEnum
{
    local_camera,
    global_camera,
    follow_camera,
    local_camera_new
};

enum class PixelFormat
{
    Default,
    Mono8,
    BGR8
};

struct ImageRecord
{
    cv::Mat image;
    TimePoint timepoint;

    ImageRecord();
    void clear();
};

struct BinocularRecord
{
    cv::Mat left_image;
    cv::Mat right_image;
    //cv::Mat bino_image;
    TimePoint timepoint;

    BinocularRecord();
    void clear();
};

#endif  //CAMERA_NODE_CORE_UTILS_DEFINITIONS_HPP_
