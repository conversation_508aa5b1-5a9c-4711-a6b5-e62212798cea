#include "HikCamera.hpp"

#include "CameraObseverBase.hpp"

#include <rclcpp/rclcpp.hpp>

using namespace std::literals::chrono_literals;

static const rclcpp::Logger kLogger = rclcpp::get_logger("HikCamera");

HikCamera::HikCamera() {}

HikCamera::HikCamera(CameraTypeEnum cam_type /*int cam_id_left, int cam_id_right*/)
    : camera_type_(cam_type)
    , left_id_(-1)
    , right_id_(-1)
    , thread_monitor_{nullptr}
    , capture_left_flag_{false}
    , capture_right_flag_{false}
    , monite_flag_(true)
    , failed_count_left_(0)
    , failed_count_right_(0)
{
    //init_cam()
    camera_left_ = std::make_unique<HikCameraDev>();
    camera_right_ = std::make_unique<HikCameraDev>();

    if (camera_type_ == CameraTypeEnum::local_camera_new) {
        init_cam_ = &HikCamera::init_cam_local_new;
    }
    (this->*init_cam_)();

    thread_monitor_ = std::make_unique<std::thread>(this->monite, this);
}

HikCamera::~HikCamera()
{
    StopCamera();
}

void HikCamera::shutdown_right_capture()
{
    capture_right_flag_ = false;
    std::lock_guard<std::mutex> lck{mtx_restart_right_};
    //if (run_servo_task_right_) {
    //    run_servo_task_right_->stop();
    //    delete run_servo_task_right_;
    //    run_servo_task_right_ = nullptr;
    //}

    //if (camera_right_) {
    //    delete camera_right_;
    //    camera_right_ = nullptr;
    //}
}

void HikCamera::shutdown_left_capture()
{
    camera_left_->stop_grab();
}

bool HikCamera::restart_camera()
{
    shutdown_left_capture();
    (this->*init_cam_)();
    restart_left_cam();

    shutdown_right_capture();
    (this->*init_cam_)();
    restart_right_cam();

    return true;
}

void HikCamera::AddObserver(CameraObseverBase *pCameraObserver)
{
    std::lock_guard<std::mutex> lck{mtx_observer_camera_};
    setCameraOberser_.insert(pCameraObserver);
}

void HikCamera::RemoveObserver(CameraObseverBase *pCameraObserver)
{
    std::lock_guard<std::mutex> lck{mtx_observer_camera_};
    setCameraOberser_.erase(pCameraObserver);
}

void HikCamera::StopCamera()
{

    monite_flag_ = false;
    std::this_thread::sleep_for(1s);
    if (thread_monitor_ != nullptr) {
        thread_monitor_->join();
        thread_monitor_.reset();
        thread_monitor_ = nullptr;
    }

    shutdown_left_capture();
    shutdown_right_capture();
}

void HikCamera::NotifyLeftCameraFailed()
{
    std::set<CameraObseverBase *>::iterator iterSet;
    for (iterSet = setCameraOberser_.begin(); iterSet != setCameraOberser_.end(); ++iterSet) {
        CameraObseverBase *obs = *iterSet;
        obs->NotifyLeftCameraFailed();
    }
}

void HikCamera::NotifyRightCameraFailed()
{
    std::set<CameraObseverBase *>::iterator iterSet;
    for (iterSet = setCameraOberser_.begin(); iterSet != setCameraOberser_.end(); ++iterSet) {
        CameraObseverBase *obs = *iterSet;
        obs->NotifyRightCameraFailed();
    }
}

void HikCamera::NotifyLeftCameraSucced()
{
    std::set<CameraObseverBase *>::iterator iterSet;
    for (iterSet = setCameraOberser_.begin(); iterSet != setCameraOberser_.end(); ++iterSet) {
        CameraObseverBase *obs = *iterSet;
        obs->NotifyLeftCameraSucced();
    }
}

void HikCamera::NotifyRightCameraSucced()
{
    std::set<CameraObseverBase *>::iterator iterSet;
    for (iterSet = setCameraOberser_.begin(); iterSet != setCameraOberser_.end(); ++iterSet) {
        CameraObseverBase *obs = *iterSet;
        obs->NotifyRightCameraSucced();
    }
}

bool HikCamera::start_left_capture()
{
    return camera_left_->start_grab();
}

bool HikCamera::start_right_capture()
{
    return camera_right_->start_grab();
    //throw std::logic_error("Not implemented yet");
    //if (!capture_right_flag_) {
    //    //shutdown_rightcapture();
    //    //(this->*init_cam_)();
    //    //restart_right_cam();
    //    std::this_thread::sleep_for(50ms);
    //}

    ////failed_count_right_ = 0;
    ////capture_right_flag_ = true;

    //return true;
}

void HikCamera::monite(HikCamera *const self)
{
    static bool left_notify_suc = false;
    static bool right_notify_suc = false;
    const int lmt = 24;  //empirical value
    static int left_cnt = 0, right_cnt = 0;
    //static bool left_h_lvl_previous, right_h_lvl_previous;

    while (self->monite_flag_) {
        if (!self->capture_left_flag_) {
            if (self->suc_capture_left_flag_) {
                self->suc_capture_left_flag_ = false;
            }
            self->restart_failed_left_++;
            if (self->restart_failed_left_ > 3) {
                self->restart_failed_left_ = 0;
                self->NotifyLeftCameraFailed();
            }
            self->start_left_capture();
            left_notify_suc = false;
        }
        else {
            if (!left_notify_suc && self->suc_capture_left_flag_) {
                self->NotifyLeftCameraSucced();
                left_notify_suc = true;
            }
        }

        if (!self->capture_right_flag_) {
            if (self->suc_capture_right_flag_) {
                self->suc_capture_right_flag_ = false;
            }
            self->restart_failed_right_++;
            if (self->restart_failed_right_ > 3) {
                self->restart_failed_right_ = 0;
                self->NotifyRightCameraFailed();
            }
            self->start_right_capture();
            right_notify_suc = false;
        }
        else {
            if (!right_notify_suc && self->suc_capture_right_flag_) {
                self->NotifyRightCameraSucced();
                right_notify_suc = true;
            }
        }

        if (self->camera_type_ != CameraTypeEnum::global_camera) {
            continue;
        }

        if (right_cnt > lmt || left_cnt > lmt) {
            self->monite_flag_ = false;
            self->capture_left_flag_ = false;
            self->capture_right_flag_ = false;
            self->shutdown_left_capture();
            self->shutdown_right_capture();
            (self->*self->init_cam_)();
            RCLCPP_INFO_STREAM(
                kLogger, __FUNCTION__ << ": 2right_cnt=" << right_cnt << ", left_cnt=" << left_cnt);

            self->monite_flag_ = true;
            left_cnt = 0;
            right_cnt = 0;
        }

        std::this_thread::sleep_for(50ms);
    }
}

bool HikCamera::set_binocular_device_id(int &id_left, int &id_right)
{
    throw std::logic_error("Not implemented yet" + id_left + id_right);
    return false;
}

void HikCamera::restart_left_cam()
{
    std::lock_guard<std::mutex> lck{mtx_restart_left_};
    if (camera_left_) {
        camera_left_.reset();
    }

    //TODO: params

    camera_left_ = std::make_unique<HikCameraDev>();
}

void HikCamera::restart_right_cam()
{
    std::lock_guard<std::mutex> lck{mtx_restart_right_};
    if (camera_right_) {
        camera_right_.reset();
    }

    //TODO: params
    camera_right_ = std::make_unique<HikCameraDev>();
}

void HikCamera::init_cam()
{
    throw std::logic_error("Not implemented yet");
}

void HikCamera::init_cam_local_new()
{
    bool l, r;
    left_id_ = 0;
    right_id_ = 1;

    l = camera_left_->init(left_id_);
    r = camera_right_->init(right_id_);

    if (!l || !r) {
        std::cout << __FUNCTION__ << ": init(): l=" << l << ", r=" << r << std::endl;
        throw std::runtime_error("init failed!");
    }

    l = camera_left_->configure(3072, 2048, 60, 10, PixelType_Gvsp_Mono8);
    r = camera_right_->configure(3072, 2048, 60, 10, PixelType_Gvsp_Mono8);
    //l = camera_left_->configure(640, 480, 60, 10, PixelType_Gvsp_Mono8);
    //r = camera_right_->configure(640, 480, 60, 10, PixelType_Gvsp_Mono8);

    if (!l || !r) {
        std::cout << __FUNCTION__ << ": init(): l=" << l << ", r=" << r << std::endl;
    }
}

bool HikCamera::set_brightness(int nBrightness)
{

    auto rt = camera_left_->set_brightness(nBrightness);
    rt = rt && camera_right_->set_brightness(nBrightness);
    return rt;
}

bool HikCamera::set_resolution(int width, int hight)
{
    auto rt1 = camera_left_->set_resolution(width, hight);
    auto rt2 = camera_right_->set_resolution(width, hight);
    return rt1 && rt2;
}

bool HikCamera::set_expose_time(std::chrono::milliseconds t_ms)
{
    auto t_ms_c = t_ms.count();
    auto rt = camera_left_->set_exposure(t_ms_c);
    rt = rt && camera_right_->set_exposure(t_ms_c);
    return rt;
}

bool HikCamera::set_format(PixelFormat fmt)
{
    auto rt = camera_left_->set_pixel_format(fmt);
    rt = rt && camera_right_->set_pixel_format(fmt);
    return false;
}

bool HikCamera::set_fps(int fps)
{
    auto rt = camera_left_->set_fps(fps);
    rt = rt && camera_right_->set_fps(fps);
    return false;
}

int HikCamera::get_fps()
{
    return camera_left_->get_fps();
}

PixelFormat HikCamera::get_format()
{
    if (camera_left_ != nullptr && camera_right_ != nullptr) {
        return camera_left_->get_pixel_format();
    }

    return PixelFormat::Default;
}

bool HikCamera::get_left_image(ImageRecord &rcd)
{
    rcd = camera_left_->get_one_frame_safe();
    if (rcd.image.empty() || rcd.timepoint.time_since_epoch().count() == 0) {
        return false;
    }
    return true;
}

bool HikCamera::get_right_image(ImageRecord &rcd)
{
    rcd = camera_right_->get_one_frame_safe();

    if (rcd.image.empty() || rcd.timepoint.time_since_epoch().count() == 0) {
        return false;
    }
    return true;
}

bool HikCamera::get_bino_image_loop(BinocularRecord &rcd)
{
    using std::chrono::duration_cast;
    using std::chrono::microseconds;

    static constexpr auto DiffThreshold = 37ms;  //1000 / 60 * 2 + 5
    static constexpr auto DiffThreshold_us = duration_cast<microseconds>(DiffThreshold).count();
    bool tp_flag = false;

    while (true) {
        auto rcd_l = camera_left_->get_latest();
        auto rcd_r = camera_right_->get_latest();

        auto tp_now = std::chrono::steady_clock::now();

        auto dur_left_right =
            duration_cast<microseconds>(rcd_l.timepoint - rcd_r.timepoint).count();
        auto dur_past_now =
            duration_cast<microseconds>(tp_now - std::min(rcd_l.timepoint, rcd_r.timepoint))
                .count();

        tp_flag = (std::abs(dur_left_right) < DiffThreshold_us) &&
                  (std::abs(dur_past_now) < DiffThreshold_us);

        if (tp_flag) {
            rcd.left_image = rcd_l.image;
            rcd.right_image = rcd_r.image;
            rcd.timepoint = std::min(rcd_l.timepoint, rcd_r.timepoint);
            return true;
        }
    }
    return false;
}
