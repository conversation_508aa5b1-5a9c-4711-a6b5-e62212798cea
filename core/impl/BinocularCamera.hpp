#ifndef GLOBALALGORITHM_INCLUD_CAMERA_BINOCULARCAMERA_HPP_
#define GLOBALALGORITHM_INCLUD_CAMERA_BINOCULARCAMERA_HPP_

#include <dirent.h>

#include "definitions.hpp"

class CameraObseverBase;

class BinocularCamera {
public:
    virtual ~BinocularCamera();

    //观察者
    /** AddObserver 添加观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    virtual void AddObserver(CameraObseverBase *pCameraObserver) = 0;

    /** RemoveObserver 删除观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    virtual void RemoveObserver(CameraObseverBase *pCameraObserver) = 0;

    //params
    virtual bool set_brightness(int nBrightness) = 0;
    virtual bool set_fps(int fps) = 0;
    virtual int get_fps() = 0;
    virtual bool set_resolution(int height, int width) = 0;
    virtual bool set_expose_time(std::chrono::milliseconds rel_time) = 0;

    template<typename Duration>
    bool set_expose_time(Duration dur)
    {
        return set_expose_time(std::chrono::duration_cast<std::chrono::milliseconds>(dur));
    }

    virtual bool set_format(PixelFormat) = 0;
    virtual PixelFormat get_format() = 0;

    //control
    virtual void init_cam() = 0;
    virtual void StopCamera() = 0;
    virtual bool restart_camera() = 0;

public:
    virtual bool get_left_image(ImageRecord &rcd) = 0;
    virtual bool get_right_image(ImageRecord &rcd) = 0;
    virtual bool get_bino_image_loop(BinocularRecord &rcd) = 0;

private:
    /** restart_left_cam 重启左眼相机
     *
     * @return  无
     **/
    virtual void restart_left_cam() = 0;

    /** restart_right_cam 重启右眼相机
     *
     * @return  无
     **/
    virtual void restart_right_cam() = 0;

    /** start_left_capture 开始左侧相机抓图线程
     *
     * @return  无
     **/
    virtual bool start_left_capture() = 0;

    /** start_right_capture 开始右侧相机抓图线程
     *
     * @return  无
     **/
    virtual bool start_right_capture() = 0;

    /** shutdown_left_capture 关闭右侧相机抓图
     *
     * @return  无
     **/
    virtual void shutdown_left_capture() = 0;

    /** shutdown_right_capture 关闭左侧相机抓图
     *
     * @return  无
     **/
    virtual void shutdown_right_capture() = 0;
};

#endif  //GLOBALALGORITHM_INCLUD_CAMERA_BINOCULARCAMERA_HPP_
