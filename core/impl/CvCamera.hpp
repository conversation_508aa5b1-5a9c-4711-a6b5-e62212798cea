#ifndef GLOBALALGORITHM_INCLUD_CAMERA_CVCAMERA_HPP_
#define GLOBALALGORITHM_INCLUD_CAMERA_CVCAMERA_HPP_

#include <dirent.h>
#include <opencv2/opencv.hpp>

#include "V4l2CameraDev.hpp"

#include "BinocularCamera.hpp"
#include "definitions.hpp"

class CvCamera : public BinocularCamera {
protected:
    CvCamera();

public:
    CvCamera(CameraTypeEnum cam_type /*int cam_id_left, int cam_id_right*/);
    virtual ~CvCamera();

    void StopCamera() override;

    void init_cam() override;
    bool set_brightness(int nBrightness) override;
    bool set_resolution(int, int) override;
    bool set_expose_time(std::chrono::milliseconds) override;
    bool set_format(PixelFormat) override;
    bool set_fps(int fps) override;

    PixelFormat get_format() override;
    int get_fps() override;

    bool get_left_image(ImageRecord &rcd) override;
    bool get_right_image(ImageRecord &rcd) override;
    bool get_bino_image_loop(BinocularRecord &rcd) override;

public:
    /** AddObserver 添加观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    void AddObserver(CameraObseverBase *pCameraObserver) override;

    /** RemoveObserver 删除观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    void RemoveObserver(CameraObseverBase *pCameraObserver) override;

private:
    void init_bino_idx_for_global();
    void init_bino_idx_for_local_legacy();
    void set_v4l2_params_for_global();
    void set_v4l2_params_for_local();

    /** restart_camera 重启相机
     *
     * @return  函数执行的结果  1 成功  1、失败
     **/
    bool restart_camera() override;

    void restart_left_cam() override;
    void restart_right_cam() override;

    bool start_left_capture() override;
    bool start_right_capture() override;

    void shutdown_left_capture() override;
    void shutdown_right_capture() override;

    /** monite 左右相机线程监控
     *
     * @return  无
     **/
    static void monite(CvCamera *const);

    /** get_bus_position 获取总线位置
     * @param1 camera_path 相机路径
     * @return  无
     **/
    static std::pair<int, int> get_bus_position(const std::string camera_path);

    /** set_bino_bus_addr 设置总线地址
     * @param1 left_pos  左侧相机位置
     * @param2 right_pos  右侧相机位置
     * @return  无
     **/
    static bool set_bino_bus_addr(std::pair<int, int> &left_pos, std::pair<int, int> &right_pos);

    /** set_binocular_device_id 设置设备Id
     * @param1 id_left  左侧相机id
     * @param2 id_right  右侧相机id
     * @return  无
     **/
    static bool set_binocular_device_id(int &id_left, int &id_right);

    static std::vector<std::string> search_usb_video_devs();

private:
    CameraTypeEnum camera_type_;
    int left_id_, right_id_;
    int format_ = V4L2_PIX_FMT_MJPEG;
    int height_ = 992;
    int width_ = 992;

    std::unique_ptr<V4L2DeviceParameters> params_left_;
    std::unique_ptr<V4L2DeviceParameters> params_right_;
    std::unique_ptr<V4l2CameraDev> camera_left_;
    std::unique_ptr<V4l2CameraDev> camera_right_;

    std::unique_ptr<std::thread> thread_monitor_;

    //控制相关
    //std::mutex mtx_restart_left_;
    //std::mutex mtx_restart_right_;

    std::mutex mtx_observer_camera_;

    std::atomic_bool monite_flag_;
    std::atomic_bool capture_left_flag_ = false;
    std::atomic_bool capture_right_flag_ = false;

    std::set<CameraObseverBase *> setCameraOberser_;
};

#endif  //GLOBALALGORITHM_INCLUD_CAMERA_CVCAMERA_HPP_
