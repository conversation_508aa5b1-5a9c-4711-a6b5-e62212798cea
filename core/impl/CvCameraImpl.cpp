#include "CvCamera.hpp"

#include <charconv>
#include <sstream>
#include <string>

#include <dirent.h>
#include <fcntl.h>  //open
#include <stdio.h>
#include <unistd.h>  //close

#include <libudev.h>
#include <libusb-1.0/libusb.h>
#include <linux/videodev2.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <rclcpp/rclcpp.hpp>

#include "V4l2Capture.h"

using namespace std::literals::chrono_literals;

static const rclcpp::Logger kLogger = rclcpp::get_logger("V4l2BinoCamera");

CvCamera::CvCamera() {}

CvCamera::CvCamera(CameraTypeEnum cam_type)
    : camera_type_(cam_type)
    , left_id_(-1)
    , right_id_(-1)
    , params_left_{nullptr}
    , params_right_{nullptr}
    , camera_left_{nullptr}
    , camera_right_{nullptr}
    , thread_monitor_{nullptr}
    , monite_flag_(true)
{
    if (camera_type_ == CameraTypeEnum::follow_camera) {
        throw std::runtime_error("follow_camera 2b impl");
    }
    else if (camera_type_ == CameraTypeEnum::global_camera) {
        init_bino_idx_for_global();
        set_v4l2_params_for_global();
    }
    else if (camera_type_ == CameraTypeEnum::local_camera) {
        init_bino_idx_for_local_legacy();
        set_v4l2_params_for_local();
    }

    camera_left_ = std::make_unique<V4l2CameraDev>();
    camera_right_ = std::make_unique<V4l2CameraDev>();

    init_cam();

    thread_monitor_ = std::make_unique<std::thread>(this->monite, this);
}

CvCamera::~CvCamera()
{
    StopCamera();
}

void CvCamera::init_cam()
{
    if (!capture_left_flag_) {
        capture_left_flag_ = camera_left_->start(left_id_, *params_left_);
    }

    if (!capture_right_flag_) {
        capture_right_flag_ = camera_right_->start(right_id_, *params_right_);
    }
}

void CvCamera::shutdown_right_capture()
{
    camera_right_->stop();
    //camera_right_->shutdown();
}

void CvCamera::shutdown_left_capture()
{
    camera_left_->stop();
    //need sync control status flag
}

bool CvCamera::restart_camera()
{
    camera_left_->stop();
    camera_right_->stop();

    std::this_thread::sleep_for(100ms);

    if (!camera_left_->start(left_id_, *params_left_)) {
        return false;
    }
    if (!camera_right_->start(right_id_, *params_left_)) {
        return false;
    }
    return true;
}

void CvCamera::StopCamera()
{
    monite_flag_ = false;
    std::this_thread::sleep_for(1s);
    if (thread_monitor_ != nullptr) {
        thread_monitor_->join();
        thread_monitor_.reset();
        thread_monitor_ = nullptr;
    }

    shutdown_left_capture();
    shutdown_right_capture();
}

bool CvCamera::start_left_capture()
{
    if (capture_left_flag_) {
        return true;
    }
    else {
        capture_left_flag_ = camera_left_->start(left_id_, *params_left_);
        return capture_left_flag_;
    }
}

bool CvCamera::start_right_capture()
{
    if (capture_right_flag_) {
        return true;
    }
    else {
        capture_right_flag_ = camera_right_->start(right_id_, *params_right_);
        return capture_right_flag_;
    }
}

void CvCamera::monite(CvCamera *const self)
{
    constexpr int LMT = 24;  //empirical value
    static int left_cnt = 0, right_cnt = 0;

    while (self->monite_flag_) {
        if (!self->capture_left_flag_) {
            if (self->start_left_capture()) {
                self->capture_left_flag_ = true;
            }
            else {
                left_cnt++;
            }
        }

        if (!self->capture_right_flag_) {
            if (self->start_right_capture()) {
                self->capture_right_flag_ = true;
            }
            else {
                right_cnt++;
            }
        }

        if (right_cnt > LMT || left_cnt > LMT) {
            self->monite_flag_ = false;
            self->capture_left_flag_ = false;
            self->capture_right_flag_ = false;
            self->shutdown_left_capture();
            self->shutdown_right_capture();
            self->init_cam();
            RCLCPP_INFO_STREAM(
                kLogger, __FUNCTION__ << ": 1right_cnt=" << right_cnt << ", left_cnt=" << left_cnt);

            self->monite_flag_ = true;
            left_cnt = 0;
            right_cnt = 0;
        }

        std::this_thread::sleep_for(50ms);
    }
}

void CvCamera::init_bino_idx_for_global()
{

    left_id_ = -1;
    right_id_ = -1;
    std::vector<std::string> devs{};
    {  //read carmera devs
        DIR *dfd;
        const std::string pathname = "/dev";
        dfd = opendir(pathname.c_str());
        dirent *dp = nullptr;
        while ((dp = readdir(dfd)) != nullptr) {
            if (strncmp(dp->d_name, ".", 1) == 0)
                continue; /* 跳过当前文件夹和上一层文件夹以及隐藏文件*/

            std::string name{dp->d_name};
            if (strncmp(dp->d_name, "video", 5) == 0 && strlen(dp->d_name) <= 7) {
                std::string dev = pathname + "/" + dp->d_name;
                devs.push_back(dev);
            }
        }
        closedir(dfd);
    }

    v4l2_capability cap;
    for (const auto &name : devs) {
        int vfd = open(name.c_str(), O_RDWR);
        if (vfd < 0) {
            //need not close
            continue;
        }

        if (ioctl(vfd, VIDIOC_QUERYCAP, &cap) == -1) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }

        int dev_num_cur =
            atoi(name.substr(10).c_str());  //10 = len for "/dev/video", to get the num behind

        if (strncmp((const char *)cap.card, "FueCamLeft", 10) == 0) {
            left_id_ = dev_num_cur;
        }
        else if (strncmp((const char *)cap.card, "FueCamRight", 11) == 0) {
            right_id_ = dev_num_cur;
        }

        close(vfd);
    }
    return;
}

void CvCamera::init_bino_idx_for_local_legacy()
{
    v4l2_capability cap;
    auto video_devs = search_usb_video_devs();
    for (const auto &name : video_devs) {
        int vfd = open(name.c_str(), O_RDWR);
        if (vfd < 0) {
            //need not close
            continue;
        }
        if (ioctl(vfd, VIDIOC_QUERYCAP, &cap) == -1) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }

        int dev_num_cur =
            atoi(name.substr(10).c_str());  //10 = len for "/dev/video", to get the num behind

        if (strncmp((const char *)cap.card, "SPCA2100", 8) == 0) {
            if (left_id_ == -1) {
                ;
                left_id_ = dev_num_cur;
            }
            else {
                right_id_ = dev_num_cur;
                if (left_id_ > right_id_) {  //keep smaller one on left!
                    std::swap(right_id_, left_id_);
                }
            }
        }

        close(vfd);
    }

    RCLCPP_WARN_STREAM(kLogger,
                       __FUNCTION__ << ": before set_binocular_device_id()"
                                    << ", left_id=" << left_id_ << ", right_id=" << right_id_);
    auto rlt = set_binocular_device_id(left_id_, right_id_);
    RCLCPP_WARN_STREAM(kLogger,
                       __FUNCTION__ << ": set_binocular_device_id() rt=" << rlt
                                    << ", left_id=" << left_id_ << ", right_id=" << right_id_);
}

void CvCamera::set_v4l2_params_for_global()
{
    std::ostringstream oss_left, oss_right;
    oss_left << "/dev/video" << left_id_;
    oss_right << "/dev/video" << right_id_;
    int verbose = 0;
    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    width_ = 4000;
    height_ = 3000;
    int fps = 10;
    params_left_ = std::make_unique<V4L2DeviceParameters>(
        oss_left.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    params_right_ = std::make_unique<V4L2DeviceParameters>(
        oss_right.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
}

void CvCamera::set_v4l2_params_for_local()
{
    std::ostringstream oss_left, oss_right;
    oss_left << "/dev/video" << left_id_;
    oss_right << "/dev/video" << right_id_;
    int verbose = 0;
    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    width_ = 992;
    height_ = 992;
    int fps = 60;
    params_left_ = std::make_unique<V4L2DeviceParameters>(
        oss_left.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    params_right_ = std::make_unique<V4L2DeviceParameters>(
        oss_right.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
}

int query_global_camera()
{
    int rt = 0;
    std::vector<std::string> devs{};

    {  //read carmera devs
        DIR *dfd;
        const std::string pathname = "/dev";
        dfd = opendir(pathname.c_str());
        dirent *dp = nullptr;
        while ((dp = readdir(dfd)) != nullptr) {
            if (strncmp(dp->d_name, ".", 1) == 0)
                continue; /* 跳过当前文件夹和上一层文件夹以及隐藏文件*/

            std::string name{dp->d_name};
            if (strncmp(dp->d_name, "video", 5) == 0 && strlen(dp->d_name) <= 7) {
                std::string dev = pathname + "/" + dp->d_name;
                devs.push_back(dev);
            }
        }
        closedir(dfd);
    }

    v4l2_capability cap;
    for (const auto &name : devs) {
        int vfd = open(name.c_str(), O_RDWR);
        if (vfd < 0) {
            //need not close
            continue;
        }

        if (ioctl(vfd, VIDIOC_QUERYCAP, &cap) == -1) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }
        if (!(V4L2_CAP_VIDEO_CAPTURE & cap.device_caps)) {
            close(vfd);
            continue;
        }

        //int dev_num_cur =
        //    atoi(name.substr(10).c_str());  //10 = len for "/dev/video", to get the num behind

        if (strncmp((const char *)cap.card, "FueCamLeft", 10) == 0) {
            rt = rt | 0x01;
        }
        else if (strncmp((const char *)cap.card, "FueCamRight", 11) == 0) {
            rt = rt | 0x10;
        }

        close(vfd);
    }
    return rt;
}

std::vector<std::string> CvCamera::search_usb_video_devs()
{
    std::vector<std::string> devs{};
    DIR *dfd;
    const std::string pathname = "/dev";

    //read carmera devs
    dfd = opendir(pathname.c_str());
    dirent *dp = nullptr;
    while ((dp = readdir(dfd)) != nullptr) {
        if (strncmp(dp->d_name, ".", 1) == 0) {
            continue; /* 跳过当前文件夹和上一层文件夹以及隐藏文件*/
        }

        std::string name{dp->d_name};
        if (strncmp(dp->d_name, "video", 5) == 0 && strlen(dp->d_name) <= 7) {
            std::string dev = pathname + "/" + dp->d_name;
            devs.push_back(dev);
        }
    }
    closedir(dfd);

    return devs;
}

std::pair<int, int> CvCamera::get_bus_position(const std::string camera_path)
{
    auto rt = std::pair<int, int>{};
    if (camera_path.empty()) {
        return rt;
    }

    struct udev *udev = udev_new();
    if (!udev) {
        std::cerr << "Cannot create udev context" << std::endl;
        return rt;
    }

    struct udev_device *dev = udev_device_new_from_subsystem_sysname(
        udev, "video4linux", camera_path.c_str());  //device_path.c_str());
    if (!dev) {
        std::cerr << "Cannot get udev device for " << camera_path << std::endl;
        udev_unref(udev);
        return rt;
    }

    //const char *syspath = udev_device_get_syspath(dev);

    struct udev_device *parent =
        udev_device_get_parent_with_subsystem_devtype(dev, "usb", "usb_device");
    if (!parent) {
        std::cerr << "Cannot find parent USB device" << std::endl;
        udev_device_unref(dev);
        udev_unref(udev);
        return rt;
    }

    const char *busnum = udev_device_get_sysattr_value(parent, "busnum");
    auto bus = atoi(busnum);

    const char *devpath = udev_device_get_devpath(parent);
    auto str_devpath = std::string(devpath);
    auto pos_devaddr = str_devpath.find_last_of('-') + 1;
    auto len_devaddr = str_devpath.size() - pos_devaddr;
    auto add = std::stoi(str_devpath.substr(pos_devaddr, len_devaddr));

    udev_device_unref(dev);
    udev_unref(udev);

    rt.first = bus;
    rt.second = add;
    return rt;
}

bool CvCamera::set_bino_bus_addr(std::pair<int, int> &left_pos, std::pair<int, int> &right_pos)
{
    left_pos.first = -1;
    left_pos.second = -1;
    right_pos.first = -1;
    right_pos.second = -1;

    libusb_device **devs;
    libusb_context *ctx = NULL;
    libusb_device_handle *handle = nullptr;

    int r = libusb_init(&ctx);
    if (r < 0) {
        //RCLCPP_ERROR_STREAM(kLogger, __FUNCTION__ << ": libusb_init ERROR!!");
        return false;
    }

    auto cnt = libusb_get_device_list(ctx, &devs);
    if (cnt < 0) {
        //RCLCPP_ERROR_STREAM(kLogger, __FUNCTION__ << ": libusb_get_device_list ERROR!!");
        return false;
    }

    //traverse dev list
    for (ssize_t i = 0; i < cnt; i++) {
        libusb_device *dev = devs[i];
        libusb_device_descriptor desc;

        //struct stat dev_stat;
        //char sys_path[PATH_MAX];
        //snprintf(sys_path,
        //        sizeof(sys_path),
        //        "/sys/bus/usb/devices/%d-%d",
        //        libusb_get_bus_number(dev),
        //        libusb_get_device_address(dev));
        //stat(sys_path, &dev_stat);
        //std::cout << sys_path << "'s st.st_rdev=" << dev_stat.st_rdev << ", dev=" <<
        //dev_stat.st_dev
        //         << std::endl;

        auto bus_num = libusb_get_bus_number(dev);
        //auto dev_addr = libusb_get_device_address(dev);
        auto port_num = libusb_get_port_number(dev);

        //std::cout << "bus_num-port_num="<<(int)bus_num << "-"<<(int)port_num <<std::endl;

        r = libusb_get_device_descriptor(dev, &desc);
        if (r < 0) {
            //RCLCPP_WARN_STREAM(kLogger, __FUNCTION__ << ": Failed to get device descriptor");
            continue;
        }
        if (desc.idProduct != 0x2ca9) {
            //RCLCPP_INFO_STREAM(kLogger, __FUNCTION__ << ": jump non-SPCA2100 device.");
            continue;
        }

        r = libusb_open(dev, &handle);
        if (r < 0) {
            //RCLCPP_WARN_STREAM(kLogger, __FUNCTION__ << ": Failed to open device");
            continue;
        }

        //Assuming we are interested in the first interface
        libusb_config_descriptor *config;
        r = libusb_get_active_config_descriptor(dev, &config);
        if (r != LIBUSB_SUCCESS) {
            //RCLCPP_WARN_STREAM(kLogger,
            //                   __FUNCTION__ << ": libusb_get_active_config_descriptor() "
            //                                << "failed on dev=" << dev);
            libusb_close(handle);
            continue;
        }

        for (int j = 0; j < config->bNumInterfaces; j++) {
            const libusb_interface *interface = &config->interface[j];
            for (int k = 0; k < interface->num_altsetting; k++) {
                const libusb_interface_descriptor *interface_desc = &interface->altsetting[k];
                if (interface_desc->iInterface != 0) {
                    unsigned char serial[1024];
                    //int r = libusb_get_string_descriptor_ascii(
                    //handle, interface_desc->iInterface, serial, 1024);
                    //std::cout << "r=" << r << ", serial=" << serial << std::endl;
                    libusb_get_string_descriptor_ascii(
                        handle, interface_desc->iInterface, serial, 1024);

                    auto interface_str = std::string((char *)serial);
                    if (interface_str.find("Left") != std::string::npos) {
                        left_pos.first = bus_num;
                        left_pos.second = port_num;
                        {
                        }
                        break;
                    }
                    else if (interface_str.find("Right") != std::string::npos) {
                        right_pos.first = bus_num;
                        right_pos.second = port_num;
                        break;
                    }
                }
            }
        }
        libusb_free_config_descriptor(config);
        libusb_close(handle);
    }

    libusb_free_device_list(devs, 1);
    libusb_exit(ctx);

    if (left_pos.first == -1) {
        //RCLCPP_WARN_STREAM(kLogger, __FUNCTION__ << ": Failed to find left_pos");
    }
    if (right_pos.first == -1) {
        //RCLCPP_WARN_STREAM(kLogger, __FUNCTION__ << ": Failed to find right_pos");
    }

    return left_pos.first != -1 && right_pos.first != -1;
}

bool CvCamera::set_binocular_device_id(int &id_left, int &id_right)
{

    int id1 = id_left;
    int id2 = id_right;
    id_left = -1;
    id_right = -1;

    std::string name1 = "video" + std::to_string(id1);
    std::string name2 = "video" + std::to_string(id2);

    auto bus_pos1 = get_bus_position(name1);
    auto bus_pos2 = get_bus_position(name2);

    std::pair<int, int> left_pos, right_pos;
    if (!set_bino_bus_addr(left_pos, right_pos)) {
        //RCLCPP_ERROR_STREAM(kLogger, __FUNCTION__ << ": set_bino_bus_addr() failed!!");
        return false;
    }

    if (left_pos == bus_pos1) {
        id_left = id1;
    }
    else if (left_pos == bus_pos2) {
        id_left = id2;
    }

    if (right_pos == bus_pos1) {
        id_right = id1;
    }
    else if (right_pos == bus_pos2) {
        id_right = id2;
    }

    if (id_left < 0 || id_right < 0) {
        //RCLCPP_ERROR_STREAM(
        //    kLogger, __FUNCTION__ << ": id_left < 0 || id_right < 0 !! using old id value...");
        id_left = id1;
        id_right = id2;
        return false;
    }
    return true;
}

void CvCamera::restart_left_cam()
{

    //if (camera_type_ == CameraTypeEnum::global_camera) {
    //    std::ostringstream oss_left;
    //    oss_left << "/dev/video" << left_id_;
    //    int verbose = 0;
    //    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    //    width_ = 4000;
    //    height_ = 3000;
    //    int fps = 10;
    //    V4L2DeviceParameters param(
    //        oss_left.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    //     = V4l2camera_left_Capture::create(param);
    //}
    //else {
    //    //#define test__use_new_camera_fps
    //    std::ostringstream oss_left;
    //    oss_left << "/dev/video" << left_id_;
    //    int verbose = 0;
    //    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    //    width_ = 992;
    //    height_ = 992;
    //    int fps = 60;
    //    V4L2DeviceParameters param(
    //        oss_left.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    //    camera_left_ = V4l2Capture::create(param);
    //}
}

void CvCamera::restart_right_cam()
{
    //std::lock_guard<std::mutex> lck{mtx_restart_right_};
    //if (camera_right_) {
    //    delete camera_right_;
    //    camera_right_ = nullptr;
    //}
    //if (camera_type_ == CameraTypeEnum::global_camera) {
    //    std::ostringstream oss_right;
    //    oss_right << "/dev/video" << right_id_;
    //    int verbose = 0;
    //    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    //    width_ = 4000;
    //    height_ = 3000;
    //    int fps = 10;
    //    V4L2DeviceParameters param_right(
    //        oss_right.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    //    camera_right_ = V4l2Capture::create(param_right);
    //}
    //else {
    //    //#define test__use_new_camera_fps
    //    std::ostringstream oss_right;
    //    oss_right << "/dev/video" << right_id_;
    //    int verbose = 0;
    //    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    //    width_ = 992;
    //    height_ = 992;
    //    int fps = 60;
    //    V4L2DeviceParameters param_right(
    //        oss_right.str().c_str(), format_, width_, height_, fps, ioTypeIn, verbose);
    //    camera_right_ = V4l2Capture::create(param_right);
    //}
}

bool CvCamera::set_brightness(int brightness)
{
    if (camera_type_ != CameraTypeEnum::local_camera) {
        return false;
    }
    if (brightness < 0 || brightness > 3) {
        return false;
    }

    if (camera_left_ != nullptr) {

        camera_left_->set_brightness(brightness);
        return true;
    }
    if (camera_right_ != nullptr) {
        camera_right_->set_brightness(brightness);
        return true;
    }
    return false;
}

bool CvCamera::set_resolution(int w, int h)
{
    auto rt = camera_left_->set_resolution(w, h);
    rt = rt && camera_right_->set_resolution(w, h);

    return false;
}

bool CvCamera::set_expose_time(std::chrono::milliseconds)
{
    throw std::logic_error("Not implemented yet");
    return false;
}

bool CvCamera::set_format(PixelFormat fmt)
{
    return camera_left_->set_pixel_format(fmt);
}

bool CvCamera::set_fps(int fps)
{
    throw std::logic_error("Not implemented yet" + fps);
    return false;
}

PixelFormat CvCamera::get_format()
{
    if (camera_left_ != nullptr && camera_right_ != nullptr) {
        return camera_left_->get_pixel_format();
    }

    return PixelFormat::Default;
}

int CvCamera::get_fps()
{
    if (camera_left_ != nullptr && camera_right_ != nullptr) {
        return camera_left_->get_fps();
    }

    return 0;
}

bool CvCamera::get_left_image(ImageRecord &rcd)
{
    auto img = camera_left_->get_latest();
    rcd.image = img.image.clone();
    rcd.timepoint = img.timepoint;

    return !rcd.image.empty();
}

bool CvCamera::get_right_image(ImageRecord &rcd)
{
    auto img = camera_right_->get_latest();
    rcd.image = img.image.clone();
    rcd.timepoint = img.timepoint;

    return !rcd.image.empty();
}

bool CvCamera::get_bino_image_loop(BinocularRecord &rcd)
{
    using std::chrono::duration_cast;
    using std::chrono::microseconds;
    auto t_dur_num = 1000 / get_fps() * 2 + 5;
    auto DiffThreshold = std::chrono::milliseconds(t_dur_num);
    auto DiffThreshold_us = duration_cast<microseconds>(DiffThreshold).count();
    //static constexpr auto DiffThreshold = 37ms;  //1000 / 60 * 2 + 5
    //static constexpr auto DiffThreshold_us = duration_cast<microseconds>(DiffThreshold).count();
    bool tp_flag = false;

    while (true) {
        auto rcd_l = camera_left_->get_latest();
        auto rcd_r = camera_right_->get_latest();

        auto tp_now = std::chrono::steady_clock::now();

        auto dur_left_right =
            duration_cast<microseconds>(rcd_l.timepoint - rcd_r.timepoint).count();
        auto dur_past_now =
            duration_cast<microseconds>(tp_now - std::min(rcd_l.timepoint, rcd_r.timepoint))
                .count();

        tp_flag = (std::abs(dur_left_right) < DiffThreshold_us) &&
                  (std::abs(dur_past_now) < DiffThreshold_us);

        if (tp_flag) {
            rcd.left_image = rcd_l.image;
            rcd.right_image = rcd_r.image;
            rcd.timepoint = std::min(rcd_l.timepoint, rcd_r.timepoint);
            return true;
        }
    }
    return false;
}

void CvCamera::AddObserver(CameraObseverBase *pCameraObserver)
{
    std::lock_guard<std::mutex> lck{mtx_observer_camera_};
    setCameraOberser_.insert(pCameraObserver);
}

void CvCamera::RemoveObserver(CameraObseverBase *pCameraObserver)
{
    std::lock_guard<std::mutex> lck{mtx_observer_camera_};
    setCameraOberser_.erase(pCameraObserver);
}
