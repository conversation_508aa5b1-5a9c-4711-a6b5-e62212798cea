#ifndef GLOBALALGORITHM_INCLUD_CAMERA_HIKCAMERA_HPP_
#define GLOBALALGORITHM_INCLUD_CAMERA_HIKCAMERA_HPP_

#include <dirent.h>

#include "BinocularCamera.hpp"

#include "HikDev.hpp"
#include "definitions.hpp"

class HikCamera : public BinocularCamera {
protected:
    HikCamera();

public:
    HikCamera(CameraTypeEnum cam_type /*int cam_id_left, int cam_id_right*/);
    virtual ~HikCamera();

    /** AddObserver 添加观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    void AddObserver(CameraObseverBase *pCameraObserver) override;

    /** RemoveObserver 删除观察者
     * @param1 pCameraObserver 观察者指针
     * @return  无
     **/
    void RemoveObserver(CameraObseverBase *pCameraObserver) override;

    /** StopCamera 停止抓取
     * @return  无
     **/
    void StopCamera() override;

    void init_cam() override;
    bool set_brightness(int nBrightness) override;
    bool set_resolution(int width, int hight) override;
    bool set_expose_time(std::chrono::milliseconds) override;
    bool set_format(PixelFormat fmt) override;
    bool set_fps(int fps) override;
    int get_fps() override;

    PixelFormat get_format() override;

    bool get_left_image(ImageRecord &rcd) override;
    bool get_right_image(ImageRecord &rcd) override;
    bool get_bino_image_loop(BinocularRecord &rcd) override;

private:
    /** restart_camera 重启相机
     *
     * @return  函数执行的结果  1 成功  1、失败
     **/
    bool restart_camera() override;

    /** init_cam 初始化相机
     *
     * @return  无
     **/
    //void init_cam();
    void (HikCamera::*init_cam_)();
    void init_cam_local_new();

    void restart_left_cam() override;
    void restart_right_cam() override;

    bool start_left_capture() override;
    bool start_right_capture() override;

    void shutdown_left_capture() override;
    void shutdown_right_capture() override;

    /** NotifyLeftCameraFailed 通知左侧相机成功抓取失败
     *
     * @return  无
     **/
    void NotifyLeftCameraFailed();

    /** NotifyRightCameraFailed 通知右侧相机成功抓取失败
     *
     * @return  无
     **/
    void NotifyRightCameraFailed();

    /** NotifyLeftCameraSucced 通知左侧相机成功抓取到图片
     *
     * @return  无
     **/
    void NotifyLeftCameraSucced();

    /** NotifyRightCameraSucced 通知右侧相机成功抓取到图片
     *
     * @return  无
     **/
    void NotifyRightCameraSucced();

    /** monite 左右相机线程监控
     *
     * @return  无
     **/
    static void monite(HikCamera *const);

    /** set_binocular_device_id 设置设备Id
     * @param1 id_left  左侧相机id
     * @param2 id_right  右侧相机id
     * @return  无
     **/
    bool set_binocular_device_id(int &id_left, int &id_right);

private:
    CameraTypeEnum camera_type_;

    int left_id_, right_id_;

    std::unique_ptr<HikCameraDev> camera_left_ = nullptr;
    std::unique_ptr<HikCameraDev> camera_right_ = nullptr;

    std::mutex mtx_bino_record_;

    std::unique_ptr<std::thread> thread_monitor_;

    //控制相关
    std::mutex mtx_restart_left_;
    std::mutex mtx_restart_right_;
    std::mutex mtx_observer_camera_;

    std::atomic_bool capture_left_flag_ = false;
    std::atomic_bool capture_right_flag_ = false;
    std::atomic_bool suc_capture_left_flag_ = false;
    std::atomic_bool suc_capture_right_flag_ = false;
    std::atomic_bool monite_flag_ = true;

    int failed_count_left_ = 0;
    int failed_count_right_ = 0;
    int restart_failed_left_ = 0;
    int restart_failed_right_ = 0;

    std::set<CameraObseverBase *> setCameraOberser_;

private:
    const std::chrono::milliseconds::rep LocalGetImageDuration_ = 1000 / 60 * 2 + 5;
};

#endif  //GLOBALALGORITHM_INCLUD_CAMERA_HIKCAMERA_HPP_
