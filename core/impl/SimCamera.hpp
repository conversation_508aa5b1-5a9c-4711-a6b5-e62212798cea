#ifndef GLOBALALGORITHM_INCLUD_CAMERA_SIMCAMERA_HPP_
#define GLOBALALGORITHM_INCLUD_CAMERA_SIMCAMERA_HPP_

#include <atomic>
#include <thread>

#include <opencv2/opencv.hpp>

#include "utils.hpp"

using TimePoint = std::chrono::time_point<std::chrono::steady_clock>;


class SimCamera {
public:
    SimCamera() = delete;
    SimCamera(CameraTypeEnum cam_type /*int cam_id_left, int cam_id_right*/);
    ~SimCamera();

    //std::tuple<std::shared_ptr<cv::Mat>, std::shared_ptr<cv::Mat>, TimePoint>
    BinocularRecord get_binocular_image_raw(bool imshow = false, bool imsave = false,
                                            std::string filename = "");
    bool start_capture();
    void shutdown_capture();

private:
    void init_cam();
    //void test_show(int delay);

private:
    CameraTypeEnum camera_type_;    
    std::unique_ptr<cv::VideoCapture> camera_both_;    

    cv::Mat frame_;
    int total_frames_;
    int current_frame_;
    double fps_;
};



#endif  //GLOBALALGORITHM_INCLUD_CAMERA_SIMCAMERA_HPP_
