#include "SimCamera.hpp"

#include <dirent.h>
#include <fcntl.h>  //open
#include <linux/videodev2.h>
#include <rclcpp/logging.hpp>
////#include <string.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>  //close

using namespace std::literals::chrono_literals;

const std::chrono::milliseconds::rep BinocularDiffThreshold = 1000 / 30 / 2;
static const rclcpp::Logger kLogger = rclcpp::get_logger("LaSimCamera");

SimCamera::SimCamera(CameraTypeEnum cam_type /*int cam_id_left, int cam_id_right*/)
    : camera_type_(cam_type)
    , camera_both_(nullptr)
{
    init_cam();
}

SimCamera::~SimCamera()
{
    shutdown_capture();
}

bool SimCamera::start_capture()
{
    //match interface
    return true;
}

void SimCamera::shutdown_capture()
{
    total_frames_ = 0;
    current_frame_ = 0;
    fps_ = 0;
    camera_both_->release();
    camera_both_.reset();
    return;
}

void SimCamera::init_cam()
{
    const std::string pathname = "VideoRecord.avi";
    camera_both_ = std::make_unique<cv::VideoCapture>(pathname);

    //检查是否成功打开视频文件
    if (!camera_both_->is_opened()) {
        std::cerr << "Error: Unable to open the video file." << std::endl;
        return;
    }

    //获取视频的帧率和总帧数
    fps_ = camera_both_->get(cv::CAP_PROP_FPS);
    total_frames_ = static_cast<int>(camera_both_->get(cv::CAP_PROP_FRAME_COUNT));

    std::cout << "Video Frame Rate: " << fps_ << " frames per second" << std::endl;
    std::cout << "Total Frames in Video: " << total_frames_ << std::endl;
}

//std::tuple<std::shared_ptr<cv::Mat>, std::shared_ptr<cv::Mat>, TimePoint>
BinocularRecord SimCamera::get_binocular_image_raw([[maybe_unused]] bool imshow,
                                                   [[maybe_unused]] bool imsave,
                                                   std::string filename)
{
    BinocularRecord rt;

    //从视频中读取一帧
    frame_.release();
    (*camera_both_) >> frame_;

    //检查图像是否成功加载
    if (frame_.empty()) {
        std::cerr << "Error: Unable to load the image." << std::endl;
        return rt;
    }
    ++current_frame_;

    //获取图像的宽度和高度
    int width = frame_.cols;
    int height = frame_.rows;
    int middleX = width / 2;

    //创建两个图像，分别表示左半部分和右半部分
    rt.left_image = frame_(cv::Rect(0, 0, middleX, height));
    rt.right_image = frame_(cv::Rect(middleX, 0, width - middleX, height));
    rt.timepoint = std::chrono::steady_clock::now();

    return rt;
}

//void SimCamera::test_show(int delay)
//{
//    cv::Mat image_raw_l;
//    cv::Mat image_raw_r;
//    cv::Mat img_l;
//    cv::Mat img_r;

//cv::Mat dst;
////dst.create(300, 800, img_r.type());
//while (true) {
//    auto bl = camera_left_->read(image_raw_l);
//    auto br = camera_right_->read(image_raw_r);
//    RCLCPP_INFO_STREAM(kLogger, "bl=" << bl << ", br=" << br);

//cv::resize(image_raw_l, img_l, cv::Size(400, 300));
//cv::resize(image_raw_r, img_r, cv::Size(400, 300));

//cv::hconcat(img_l, img_r, dst);
//cv::imshow("hahah", dst);

//cv::waitKey(delay);
//}
//}
