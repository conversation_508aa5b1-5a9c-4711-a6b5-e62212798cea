#include "BinocularCamera.hpp"
//#include "V4l2Capture.h"

//#include <dirent.h>
//#include <fcntl.h>  //open
//#include <linux/videodev2.h>
//#include <sstream>
//#include <stdio.h>
//#include <string>
//#include <sys/ioctl.h>
//#include <sys/stat.h>
//#include <sys/types.h>
//#include <unistd.h>  //close

//#include <charconv>
//#include <libudev.h>
//#include <libusb-1.0/libusb.h>
#include <rclcpp/rclcpp.hpp>

static const rclcpp::Logger kLogger = rclcpp::get_logger("BinocularCamera");

BinocularCamera::~BinocularCamera() {}
