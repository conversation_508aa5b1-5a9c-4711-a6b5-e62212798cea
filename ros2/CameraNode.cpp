#include <chrono>
#include <limits>

#include "BinocularCamera.hpp"
#include "CameraNode.hpp"
#include "CameraObseverBase.hpp"
//#include "camera/SystemCameraConfig.hpp"
#include "HikCamera.hpp"
#include "../nvida/src/nvcuvid.h"
#include "../nvida/src/nvEncodeAPI.h"
#include "../nvida/src/NvEncoderCuda.h"
using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

CameraNode::CameraNode(const std::string &nodename, const std::string cam_type, bool print_timing)
    : CameraNode(nodename, cam_type == "HikCamera"
                               ? std::make_unique<HikCamera>(CameraTypeEnum::local_camera_new)
                               : nullptr)
{
    print_timing_ = print_timing;
}

CameraNode::CameraNode(const std::string &nodename, std::unique_ptr<BinocularCamera> cam)
    : Node(nodename, rclcpp::NodeOptions().use_intra_process_comms(true))
    , camera_(std::move(cam))
    //, bino_record_remaped_()
    , cb_group_img_(nullptr)
    , cb_group_error_(nullptr)
    , cb_group_heartbeat_(nullptr)
    , pub_heartbeat_(nullptr)
    , pub_img_stereo_(nullptr)
    , pub_img_honcat_(nullptr)
    , pub_encode_video_(nullptr)
    , img_compressed_stereo_msg_(std::make_unique<fue_interfaces::msg::StereoImage>())
    , img_compressed_honcat_msg_(nullptr)
    , print_timing_(false)
{
    cam_obs_ = std::make_unique<CameraObseverBase>();
    camera_->AddObserver(cam_obs_.get());

    //init ros2 facilities
    cb_group_error_ = this->create_callback_group(rclcpp::CallbackGroupType::MutuallyExclusive);
    cb_group_img_ = this->create_callback_group(rclcpp::CallbackGroupType::MutuallyExclusive);
    cb_group_heartbeat_ = this->create_callback_group(rclcpp::CallbackGroupType::MutuallyExclusive);

    auto cb_sb = std::bind(&CameraNode::callback_set_brightness, this, _1, _2);
    srv_setbrightness_ = create_service<CameraBrightness>(
        "srvname_setbrightness", cb_sb, rmw_qos_profile_services_default, cb_group_error_);

    pub_img_stereo_ =
        create_publisher<fue_interfaces::msg::StereoImage>("stereo_img_from_standalone_camera", 20);

    pub_img_honcat_ = create_publisher<CompressedImage>("bino_img_compressed", 20);
    pub_encode_video_=create_publisher<std_msgs::msg::ByteMultiArray>("c",20);
    pub_heartbeat_ = create_publisher<fue_interfaces::msg::Heartbeat>(nodename + "_heartbeat", 10);

    auto task_img = std::bind(&CameraNode::publish_image_task, this);
    img_timer_ = this->create_wall_timer(17ms, task_img, cb_group_img_);
    //img_timer_ = this->create_wall_timer(20ms, task_img, cb_group_img_);

    auto task_heartbeat = std::bind(&CameraNode::publish_heartbeat_task, this);
    heartbeat_timer_ = this->create_wall_timer(500ms, task_heartbeat, cb_group_heartbeat_);

    system_error_notice_ui_client_ = create_client<fue_interfaces::srv::SystemError>(
        "ui_system_error", rmw_qos_profile_services_default, cb_group_error_);

    RCLCPP_INFO_STREAM(this->get_logger(), __FUNCTION__ << ": Init node done.");
    fflush(NULL);
}

CameraNode::~CameraNode()
{
    img_timer_->cancel();
    heartbeat_timer_->cancel();
    std::this_thread::sleep_for(300ms);

    camera_->StopCamera();
    //run_servo_task_timer_->stop();
    //run_servo_heartbeat_timer_->stop();
    //delete run_servo_task_timer_;
    //delete run_servo_heartbeat_timer_;
}

bool CameraNode::callback_set_brightness(const std::shared_ptr<CameraBrightness::Request> req,
                                         std::shared_ptr<CameraBrightness::Response> res)
{
    int nBrightness = req->brightness;
    if (camera_) {
        camera_->set_brightness(nBrightness);
        res->succ = true;
        return true;
    }
    res->succ = false;
    return false;
}

void CameraNode::publish_heartbeat_task()
{
    heartbeat_.header.frame_id = std::string(this->get_name()) + "_heartbeat";
    heartbeat_.header.stamp = this->now();
    heartbeat_.err = 0;
    heartbeat_.stat = 0;
    pub_heartbeat_->publish(heartbeat_);
}

void CameraNode::publish_image_task()
{
    //publish_image_task_honcat();
    publish_image_task_stereo();
}

void CameraNode::publish_image_task_honcat()
{
    //TODO: task lock
    static BinocularRecord rcd;
    static cv::Mat dst;

    static int cnt = 0;

    RCLCPP_INFO_STREAM(this->get_logger(), __FUNCTION__ << ": 2publish start!" << cnt);
    auto res = camera_->get_bino_image_loop(rcd);

    if (!res) {
        throw std::runtime_error(" res = camera_->get_bino_image_loop(rcd); failed!!");
        return;
    }

    if (rcd.left_image.rows == 0 || rcd.right_image.rows == 0) {
        RCLCPP_ERROR_STREAM(this->get_logger(),
                            __FUNCTION__
                                << ": bino_record_raw.left_image.rows or right_image == 0");
        return;
    }

    if (rcd.left_image.size() != rcd.right_image.size()) {
        RCLCPP_ERROR_STREAM(this->get_logger(),
                            __FUNCTION__ << ": 2rcd.left_image.size()!= rcd.right_image.size()");
        return;
    }
    cv::hconcat(rcd.left_image, rcd.right_image, dst);

    cuInit(0);
    CUdevice cuDevice = 0;
    cuDeviceGet(&cuDevice, 0);
    CUcontext cuContext = nullptr;
    cuCtxCreate(&cuContext, 0, cuDevice);

    // 创建编码器
    uint32_t width = dst.cols;
    uint32_t height = dst.rows;
    NvEncoderCuda encoder(
        cuContext,
        width,
        height,
        NV_ENC_BUFFER_FORMAT_NV12 // 推荐使用NV12，若输入为BGR需先转换
        );
    NV_ENC_INITIALIZE_PARAMS initParams = { NV_ENC_INITIALIZE_PARAMS_VER };
    NV_ENC_CONFIG encodeConfig = { NV_ENC_CONFIG_VER };
    initParams.encodeConfig = &encodeConfig;
    encoder.CreateDefaultEncoderParams(&initParams, NV_ENC_CODEC_H264_GUID, NV_ENC_PRESET_DEFAULT_GUID, NV_ENC_TUNING_INFO_HIGH_QUALITY);
    encoder.CreateEncoder(&initParams);
    const NvEncInputFrame* inputFrame = encoder.GetNextInputFrame();
    encoder.CopyToDeviceFrame(
        cuContext,
        dst_nv12.data,                // 源数据指针
        dst_nv12.step,                // 源步幅
        (CUdeviceptr)inputFrame->inputPtr, // 目标 CUDA 设备指针
        inputFrame->pitch,            // 目标步幅
        width,
        height,
        CU_MEMORYTYPE_HOST,           // Mat 数据在主机内存
        encoder.GetPixelFormat(),     // NV_ENC_BUFFER_FORMAT_NV12
        inputFrame->chromaOffsets,    // 色度平面偏移
        inputFrame->numChromaPlanes,  // 色度平面数
        false,                        // 是否非对齐拷贝
        0                             // CUDA stream
        );
    std::vector<std::vector<uint8_t>> vPacket;
    encoder.EncodeFrame(vPacket);
    auto msg = std::make_shared<std_msgs::msg::ByteMultiArray>();
    msg->data.clear();
    for (const auto& pkt : vPacket) {
        msg->data.insert(msg->data.end(), pkt.begin(), pkt.end());
    }
    msg->data.assign(vPacket, vPacket.size());
    pub_encode_video_->publish(msg);
    // vPacket 里就是编码后的视频数据（如 H264），可写文件或推流
    for (auto& pkt : vPacket) {
        // fwrite(pkt.data(), 1, pkt.size(), fp);
    }
    encoder.EndEncode(vPacket);



    RCLCPP_INFO_STREAM(this->get_logger(), __FUNCTION__ << ": publish done!" << cnt++);
}

void CameraNode::publish_image_task_stereo()
{
    //TODO: task lock
    static int cnt = 0;
    static BinocularRecord rcd;

    std::string cv_fmt_str;
    auto fmt = camera_->get_format();
    if (fmt == PixelFormat::BGR8) {
        cv_fmt_str = "bgr8";
    }
    else if (fmt == PixelFormat::Mono8) {
        cv_fmt_str = "mono8";
    }

    //RCLCPP_INFO_STREAM(this->get_logger(), __FUNCTION__ << ": 1publish start!" << cnt);

    auto res = camera_->get_bino_image_loop(rcd);
    if (!res) {
        throw std::runtime_error(" res = camera_->get_bino_image_loop(rcd); failed!!");
        return;
    }

    if (rcd.left_image.rows == 0 || rcd.right_image.rows == 0) {
        RCLCPP_ERROR_STREAM(this->get_logger(),
                            __FUNCTION__
                                << ": bino_record_raw.left_image.rows or right_image == 0");
        return;
    }

    if (rcd.left_image.size() != rcd.right_image.size()) {
        RCLCPP_ERROR_STREAM(this->get_logger(),
                            __FUNCTION__ << ": 1rcd.left_image.size()!= rcd.right_image.size()");
        return;
    }

    //cv::imwrite("xxx_pub_dst_stereo_l.jpg", rcd.left_image);
    //cv::imwrite("xxx_pub_dst_stereo_r.jpg", rcd.right_image);

    auto head = std_msgs::msg::Header();
    auto lptr = cv_bridge::CvImage(head, cv_fmt_str, rcd.left_image).toCompressedImageMsg();
    auto rptr = cv_bridge::CvImage(head, cv_fmt_str, rcd.right_image).toCompressedImageMsg();

    img_compressed_stereo_msg_ = std::make_unique<fue_interfaces::msg::StereoImage>();
    img_compressed_stereo_msg_->img_left.format = lptr->format;
    img_compressed_stereo_msg_->img_left.data = std::move(lptr->data);

    img_compressed_stereo_msg_->img_right.format = rptr->format;
    img_compressed_stereo_msg_->img_right.data = std::move(rptr->data);

    img_compressed_stereo_msg_->timestamp = this->now();
    img_compressed_stereo_msg_->img_left.header.frame_id = std::to_string(cnt);

    auto got_img_nanos =
        std::chrono::duration_cast<std::chrono::nanoseconds>(rcd.timepoint.time_since_epoch())
            .count();
    img_compressed_stereo_msg_->img_left.header.stamp.sec = got_img_nanos / 1000000000;
    img_compressed_stereo_msg_->img_left.header.stamp.nanosec = got_img_nanos % 1000000000;

    if (print_timing_) {
        RCLCPP_INFO_STREAM(this->get_logger(),
                           "***LATENCY*** captured stamp="
                               << img_compressed_stereo_msg_->img_left.header.stamp.sec << "."
                               << img_compressed_stereo_msg_->img_left.header.stamp.nanosec);
        RCLCPP_INFO_STREAM(this->get_logger(),
                           "***LATENCY*** got msg time="
                               << img_compressed_stereo_msg_->timestamp.sec << "."
                               << img_compressed_stereo_msg_->timestamp.nanosec);
        RCLCPP_INFO_STREAM(this->get_logger(), "***LATENCY*** pubed frame=" << cnt);
    }

    pub_img_stereo_->publish(std::move(img_compressed_stereo_msg_));

    if (cnt++ % 100 == 0) {
        RCLCPP_INFO_STREAM(this->get_logger(), __FUNCTION__ << ": 1publish done!" << cnt);
    }
    return;
}
