#ifndef CAMERA_NODE_CAMERANODEBUD_HPP_
#define CAMERA_NODE_CAMERANODEBUD_HPP_

#include <atomic>
#include <map>

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/compressed_image.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <std_msgs/msg/header.hpp>
#include <std_srvs/srv/trigger.hpp>

#include <cv_bridge/cv_bridge.h>  // cv_bridge converts between ROS 2 image messages and OpenCV image representations.
#include <image_transport/image_transport.hpp>  // Using image_transport allows us to publish and subscribe to compressed image streams in ROS2

//using fue_interfaces::srv::SystemError;
//using fue_interfaces::srv::CameraBrightness;

using sensor_msgs::msg::CompressedImage;
using std_srvs::srv::Trigger;

class CameraBudNode : public rclcpp::Node {
public:
    CameraBudNode(const std::string &nodename);

private:
    //rclcpp::TimerBase::SharedPtr img_timer_;

    rclcpp::CallbackGroup::SharedPtr cb_group_sub_img_;
    rclcpp::Subscription<sensor_msgs::msg::CompressedImage>::SharedPtr sub_img_bino_;
    cv_bridge::CvImagePtr img_recv_;
    //cv::Mat bino_img_;

    void callback_recv_img_compressed(const sensor_msgs::msg::CompressedImage::SharedPtr img);

    std::unique_ptr<std::thread> show_thread_;
    std::mutex show_mtx_;
    void show_image_exec();
    //void to_bino_img();
    //CompressedImage::SharedPtr img_compressed_left_msg_;
    //CompressedImage::SharedPtr img_compressed_right_msg_;
};

#endif  //CAMERA_NODE_CAMERANODEBUD_HPP_
