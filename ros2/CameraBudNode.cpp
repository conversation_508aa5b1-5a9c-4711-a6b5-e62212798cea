#include "CameraBudNode.hpp"
//#include "camera/SystemCameraConfig.hpp"

#include <chrono>
#include <iostream>
#include <limits>

#include <cv_bridge/cv_bridge.h>
#include <image_transport/image_transport.hpp>
//#include <sensor_msgs/image_encodings.h>
//#include <opencv2/highgui/highgui.hpp>
//#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;

CameraBudNode::CameraBudNode(const std::string &nodename)
    : Node(nodename)
    , img_recv_(nullptr)
{
    cb_group_sub_img_ = this->create_callback_group(rclcpp::CallbackGroupType::MutuallyExclusive);

    auto opt_sub_img = rclcpp::SubscriptionOptions();
    opt_sub_img.callback_group = cb_group_sub_img_;

    sub_img_bino_ = this->create_subscription<sensor_msgs::msg::CompressedImage>(
        "bino_img_compressed",
        20,
        std::bind(&CameraBudNode::callback_recv_img_compressed, this, std::placeholders::_1),
        opt_sub_img);

    show_thread_ = std::make_unique<std::thread>(&CameraBudNode::show_image_exec, this);
    show_thread_->detach();
}

void CameraBudNode::callback_recv_img_compressed(
    const sensor_msgs::msg::CompressedImage::SharedPtr img)
{
    if (img_recv_ == nullptr) {

        std::lock_guard<std::mutex>{show_mtx_};
        img_recv_ = cv_bridge::toCvCopy(img, "bgr8");
    }
    //cv::imwrite("recved_img.jpg", img_recv_->image);
}

void CameraBudNode::show_image_exec()
{
    static cv::Mat mat;
    //while (true) {
    //    if (img_recv_ != nullptr && !img_recv_->image.empty()) {
    //        std::lock_guard<std::mutex>{show_mtx_};
    //        mat = img_recv_->image.clone();
    //        cv::imshow("...", mat);
    //        if (cv::waitKey(1) == 27) {
    //            break;  //按下 ESC 退出
    //        }
    //    }
    //}
    while (img_recv_ == nullptr || img_recv_->image.empty()) {}

    mat = img_recv_->image.clone();
    std::cout << "sizeof(mat)=" << sizeof(mat) << std::endl;
    //while (true) {
    //cv::imshow("...", mat);
    cv::imwrite("111.jpg", mat);
    std::cout << "Type: " << mat.type() << ", Channels: " << mat.channels() << std::endl;
    std::cout << "cv::CV_8UC3 is " << CV_8UC3 << std::endl;

    //auto mmm = cv::imread("111.jpg").convertTo();
    //cv::imshow("mmm", mmm);
    // if (cv::waitKey(1) == 27) {
    //     return;  //按下 ESC 退出
    // }
}
