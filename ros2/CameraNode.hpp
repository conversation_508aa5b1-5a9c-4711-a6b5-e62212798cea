#ifndef CAMERA_NODE_CAMERANODE_HPP_
#define CAMERA_NODE_CAMERANODE_HPP_

#include <atomic>
#include <map>

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/compressed_image.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <std_msgs/msg/header.hpp>
#include <std_srvs/srv/trigger.hpp>

#include <cv_bridge/cv_bridge.h>  // cv_bridge converts between ROS 2 image messages and OpenCV image representations.
#include <image_transport/image_transport.hpp>  // Using image_transport allows us to publish and subscribe to compressed image streams in ROS2

#include "fue_interfaces/msg/heartbeat.hpp"
#include "fue_interfaces/msg/stereo_image.hpp"
#include "fue_interfaces/srv/camera_brightness.hpp"
#include "fue_interfaces/srv/system_error.hpp"
#include "std_msgs/msg/byte_multi_array.hpp"

using sensor_msgs::msg::CompressedImage;
using std_srvs::srv::Trigger;

using fue_interfaces::srv::SystemError;
using fue_interfaces::srv::CameraBrightness;

class BinocularCamera;
class CameraObseverBase;

class CameraNode : public rclcpp::Node {
public:
    CameraNode(const std::string &nodename, const std::string cam_type, bool print_timing = false);
    CameraNode(const std::string &nodename, std::unique_ptr<BinocularCamera> cam = nullptr);

    ~CameraNode();

    std::unique_ptr<BinocularCamera> camera_;
    std::unique_ptr<CameraObseverBase> cam_obs_;

private:  //ros2
    rclcpp::CallbackGroup::SharedPtr cb_group_img_;
    rclcpp::CallbackGroup::SharedPtr cb_group_error_;
    rclcpp::CallbackGroup::SharedPtr cb_group_heartbeat_;

    //心跳使用默认回调组
    rclcpp::Publisher<fue_interfaces::msg::Heartbeat>::SharedPtr pub_heartbeat_;
    fue_interfaces::msg::Heartbeat heartbeat_;
    void publish_heartbeat_task();

    rclcpp::TimerBase::SharedPtr img_timer_;
    rclcpp::TimerBase::SharedPtr heartbeat_timer_;
    void publish_image_task();
    void publish_image_task_honcat();
    void publish_image_task_stereo();

    rclcpp::Publisher<fue_interfaces::msg::StereoImage>::SharedPtr pub_img_stereo_;
    rclcpp::Publisher<CompressedImage>::SharedPtr pub_img_honcat_;
    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr pub_encode_video_;

    fue_interfaces::msg::StereoImage::UniquePtr img_compressed_stereo_msg_;
    CompressedImage::SharedPtr img_compressed_honcat_msg_;

    rclcpp::Service<CameraBrightness>::SharedPtr srv_setbrightness_;
    rclcpp::Client<fue_interfaces::srv::SystemError>::SharedPtr system_error_notice_ui_client_;
    bool callback_set_brightness(const std::shared_ptr<CameraBrightness::Request> req,
                                 std::shared_ptr<CameraBrightness::Response> res);

private:  //camera
    bool print_timing_;
};

#endif  //CAMERA_NODE_CAMERANODE_HPP_
