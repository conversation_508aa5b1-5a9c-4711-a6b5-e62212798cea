#include <fstream>
#include <functional>
#include <iostream>
#include <numeric>
#include <signal.h>
#include <string>
#include <vector>
#include <chrono>

#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>

#include <rclcpp/rclcpp.hpp>


#include "CameraBudNode.hpp"

using namespace std::chrono_literals;


void mySigintHandler(int sig)
{
    std::cout << "got sig " << sig << std::endl;
    auto rlt = rcutils_logging_shutdown();
    if (RCUTILS_RET_OK != rlt) {
        rlt++;
        std::cout << rlt << std::endl;
    }
    rclcpp::shutdown();
}

int main(int argc, char **argv)
{
    signal(SIGINT, mySigintHandler);
    signal(SIGTERM, mySigintHandler);
    signal(SIGKILL, mySigintHandler);

    rclcpp::init(argc, argv);
    auto node = std::make_shared<CameraBudNode>("camera_bud_node_001");

    rclcpp::executors::MultiThreadedExecutor exector;
    exector.add_node(node);
    exector.spin();

    rclcpp::shutdown();
    return 0;
}
