#include <chrono>
#include <fstream>
#include <functional>
#include <iostream>
#include <numeric>
#include <signal.h>
#include <string>
#include <vector>

#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>

#include "HikDev.hpp"

using namespace std::chrono_literals;

void mySigintHandler(int sig)
{
    std::cout << __FUNCTION__ << ": got sig " << sig << std::endl;
    std::terminate();
}

int main(int argc, char **argv)
{
    signal(SIGINT, mySigintHandler);
    signal(SIGTERM, mySigintHandler);
    signal(SIGKILL, mySigintHandler);

    HikCameraDev camera;
    //初始化相机
    if (!camera.init(0)) {
        std::cout << "相机初始化失败!" << std::endl;
        return -1;
    }
    std::cout << "相机初始化成功!" << std::endl;
    //设置参数
    auto fmt = PixelType_Gvsp_BayerRG8;
    //auto fmt = PixelType_Gvsp_Mono8;
    if (!camera.configure(640, 480, 60, 10, fmt)) {
        std::cout << "设置相机参数失败!" << std::endl;
        return -1;
    }
    std::cout << "设置相机参数成功!" << std::endl;
    //开始取图
    if (!camera.start_grab()) {
        std::cout << "开始取图失败!" << std::endl;
        return -1;
    }
    std::cout << "开始取图成功!" << std::endl;
    //获取10帧图像
    //for (int i = 0; i < 10; i++) {
    while (true) {
        auto mat = camera.get_one_frame_safe();
        if (mat.empty()) {
            break;
        }
        cv::imwrite("mmmmmmat.jpg", mat);
        //cv::imshow(mat);
    }
    //停止采集
    camera.stop_grab();
    std::cout << "停止采集完成!" << std::endl;
    return 0;
}
