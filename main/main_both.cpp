//#define MY_DEBUG_FOR_GLOG

//#include "stereo_calibration_mat.h"
//#include "locator.hpp"
//#include "reconstruct.hpp"
//#include "tracker.hpp"
//#include "utils.hpp"

#include <chrono>
#include <fstream>
#include <functional>
#include <iostream>
#include <numeric>
#include <signal.h>
#include <string>
#include <vector>

#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>

#include <rclcpp/rclcpp.hpp>
//#include <glog/logging.h>

#include "CameraBudNode.hpp"
#include "CameraNode.hpp"

using namespace std::chrono_literals;

void mySigintHandler(int sig)
{
    std::cout << "got sig " << sig << std::endl;
    auto rlt = rcutils_logging_shutdown();
    if (RCUTILS_RET_OK != rlt) {
        rlt++;
        std::cout << rlt << std::endl;
    }
    rclcpp::shutdown();
}

int main(int argc, char **argv)
{
    signal(SIGINT, mySigintHandler);
    signal(SIGTERM, mySigintHandler);
    signal(SIGKILL, mySigintHandler);

    rclcpp::init(argc, argv);
    auto cam_node = std::make_shared<CameraNode>("camera_node_001");
    auto show_node = std::make_shared<CameraBudNode>("camera_bud_node_001");

    rclcpp::executors::MultiThreadedExecutor exector;
    exector.add_node(cam_node);
    exector.add_node(show_node);
    exector.spin();

    rclcpp::shutdown();
    return 0;
}
