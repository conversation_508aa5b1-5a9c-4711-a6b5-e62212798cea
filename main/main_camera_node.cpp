#include <chrono>
#include <fstream>
#include <functional>
#include <iostream>
#include <numeric>
#include <string>
#include <vector>

#include <signal.h>

#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <rclcpp/rclcpp.hpp>

#include "BinocularCamera.hpp"
#include "CameraNode.hpp"
#include "CvCamera.hpp"
//#include "HikCamera.hpp"

using namespace std::chrono_literals;

void mySigintHandler(int sig)
{
    std::cout << __FUNCTION__ << ": got sig " << sig << std::endl;
    auto rlt = rcutils_logging_shutdown();
    if (RCUTILS_RET_OK != rlt) {
        rlt++;
        std::cout << rlt << std::endl;
    }
    rclcpp::shutdown();
    std::terminate();
}

int main(int argc, char **argv)
{
    signal(<PERSON><PERSON><PERSON><PERSON>, mySigintHandler);
    signal(SIG<PERSON>R<PERSON>, mySigintHandler);
    signal(S<PERSON><PERSON><PERSON>L, mySigintHandler);

    rclcpp::init(argc, argv);

    //auto bino_camera = std::make_unique<HikCamera>(CameraTypeEnum::local_camera_new);
    //bino_camera->set_resolution(3072, 2048);

    //auto bino_camera = std::make_unique<CvCamera>(CameraTypeEnum::local_camera);
    //bino_camera->set_resolution(992, 992);

    //auto bino_camera = std::make_unique<CvCamera>(CameraTypeEnum::global_camera);
    //bino_camera->set_resolution(992, 992);

    //auto cam_node = std::make_shared<CameraNode>("camera_node_only", std::move(bino_camera));
    auto cam_node = std::make_shared<CameraNode>("camera_node_only1", "HikCamera");

    rclcpp::executors::MultiThreadedExecutor exector;
    exector.add_node(cam_node);
    exector.spin();

    rclcpp::shutdown();
    return 0;
}
